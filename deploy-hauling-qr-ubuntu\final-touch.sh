#!/bin/bash

# Final Touch Script for Hauling QR Trip System
# Optimized production fixes with proper user management and environment variables
# Fixes Error 520/521 and ensures NGINX_PROXY_MODE is correctly set

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/hauling-qr-system"
UBUNTU_USER="ubuntu"
UBUNTU_HOME="/home/<USER>"
PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_fix() {
    echo -e "${CYAN}[FIX]${NC} $1"
}

# Header
echo "🎯 FINAL TOUCH: Hauling QR Trip System Production Optimization"
echo "🌐 Domain: $PRODUCTION_DOMAIN"
echo "👤 Target User: $UBUNTU_USER"
echo "📅 $(date)"
echo "=================================================="
echo

# Step 1: Stop all PM2 processes (both root and ubuntu)
log_step "1️⃣ Cleaning up existing PM2 processes..."

# Stop PM2 as root (if running)
if pm2 list 2>/dev/null | grep -q "hauling-qr-server"; then
    log_fix "Stopping PM2 processes running as root..."
    pm2 delete hauling-qr-server 2>/dev/null || true
    pm2 kill 2>/dev/null || true
fi

# Stop PM2 as ubuntu user (if running)
if sudo -u $UBUNTU_USER pm2 list 2>/dev/null | grep -q "hauling-qr-server"; then
    log_fix "Stopping PM2 processes running as ubuntu user..."
    sudo -u $UBUNTU_USER pm2 delete hauling-qr-server 2>/dev/null || true
    sudo -u $UBUNTU_USER pm2 kill 2>/dev/null || true
fi

log_success "✅ All PM2 processes cleaned up"

# Step 2: Regenerate NGINX configuration with optimizations
log_step "2️⃣ Regenerating optimized NGINX configuration..."

# Detect VPS IP
DETECTED_VPS_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "127.0.0.1")
log_info "Detected VPS IP: $DETECTED_VPS_IP"

# Create optimized NGINX configuration
sudo tee /etc/nginx/sites-available/hauling-qr-system >/dev/null <<EOF
# OPTIMIZED: Backend upstream with health checks and single instance
upstream hauling_backend {
    server localhost:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

server {
    listen 80;
    server_name ${PRODUCTION_DOMAIN} www.${PRODUCTION_DOMAIN} api.${PRODUCTION_DOMAIN} ${DETECTED_VPS_IP};

    # Real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.cloudflare.com *.cloudflareinsights.com; worker-src 'self' blob:; connect-src 'self' http: https: ws: wss: https://api.${PRODUCTION_DOMAIN} wss://api.${PRODUCTION_DOMAIN} *.cloudflare.com *.cloudflareinsights.com;" always;

    # Static frontend
    root /var/www/hauling-qr-system/client/build;
    index index.html;

    # Static assets with CORS
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${PRODUCTION_DOMAIN}";
    }

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Images static path
    location /images/ {
        alias /var/www/hauling-qr-system/client/public/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "https://${PRODUCTION_DOMAIN}";
        try_files \$uri =404;
    }

    # API with CORS - CRITICAL: Single upstream prevents Error 520
    location /api {
        # Strip upstream CORS headers to prevent duplicates
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_hide_header Access-Control-Allow-Credentials;
        proxy_hide_header Access-Control-Allow-Methods;
        proxy_hide_header Access-Control-Allow-Headers;

        # Handle preflight OPTIONS
        if (\$request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://${PRODUCTION_DOMAIN}' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        # CORS headers for actual requests
        add_header 'Access-Control-Allow-Origin' 'https://${PRODUCTION_DOMAIN}' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 30s;
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
    }

    # WebSocket support
    location /ws {
        proxy_pass http://hauling_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_read_timeout 86400;
    }
}
EOF

# Test and reload NGINX
log_info "🧪 Testing NGINX configuration..."
if sudo nginx -t; then
    log_success "✅ NGINX configuration is valid"
    sudo systemctl reload nginx
    log_success "✅ NGINX reloaded successfully"
else
    log_error "❌ NGINX configuration test failed"
    exit 1
fi

# Step 3: Create optimized ecosystem.config.js with proper user settings
log_step "3️⃣ Creating optimized PM2 ecosystem configuration..."

cd "$APP_DIR"

# Generate optimized ecosystem.config.js
cat >ecosystem.config.js <<EOF
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: 'server/server.js',
    cwd: '/var/www/hauling-qr-system',
    
    // CRITICAL: Single instance prevents Error 520/521
    instances: 1,
    exec_mode: 'cluster',
    
    // Memory and performance optimization
    max_memory_restart: '1500M',
    node_args: '--max-old-space-size=1536',
    
    // Logging configuration
    log_file: '/var/log/pm2/hauling-qr-combined.log',
    out_file: '/var/log/pm2/hauling-qr-out.log',
    error_file: '/var/log/pm2/hauling-qr-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Auto-restart configuration
    watch: false,
    ignore_watch: ['node_modules', 'logs', '*.log'],
    
    // Production environment with CRITICAL CORS settings
    env_production: {
      NODE_ENV: 'production',
      PORT: 8080,
      
      // CRITICAL: NGINX Proxy Mode - Prevents duplicate CORS headers
      NGINX_PROXY_MODE: 'true',
      EXPRESS_CORS_DISABLED: 'true',
      CORS_HANDLED_BY_NGINX: 'true',
      
      // Dynamic Domain Configuration
      PRODUCTION_DOMAIN: '${PRODUCTION_DOMAIN}',
      API_BASE_URL: 'https://api.${PRODUCTION_DOMAIN}',
      FRONTEND_URL: 'https://${PRODUCTION_DOMAIN}',
      CLIENT_URL: 'https://${PRODUCTION_DOMAIN}',
      
      // Database Configuration
      DB_HOST: 'localhost',
      DB_PORT: 5432,
      DB_NAME: 'hauling_qr_system',
      DB_USER: 'postgres',
      DB_PASSWORD: 'PostgreSQLPassword123',
      
      // Trust proxy settings
      TRUST_PROXY_IP: '127.0.0.1'
    }
  }]
};
EOF

# Set proper ownership
sudo chown $UBUNTU_USER:$UBUNTU_USER ecosystem.config.js
log_success "✅ Optimized ecosystem.config.js created"

# Step 4: Start PM2 as ubuntu user with proper environment
log_step "4️⃣ Starting PM2 as ubuntu user with optimized configuration..."

# Ensure PM2 log directory exists
sudo mkdir -p /var/log/pm2
sudo chown $UBUNTU_USER:$UBUNTU_USER /var/log/pm2

# Start PM2 as ubuntu user
log_info "🚀 Starting PM2 as ubuntu user..."
sudo -u $UBUNTU_USER bash -c "cd '$APP_DIR' && NODE_ENV=production pm2 start ecosystem.config.js --env production"
sleep 5

# Save PM2 configuration
sudo -u $UBUNTU_USER pm2 save

log_success "✅ PM2 started as ubuntu user"

# Step 5: Verify and fix NGINX_PROXY_MODE environment variable
log_step "5️⃣ Verifying and fixing NGINX_PROXY_MODE environment variable..."

# Check if NGINX_PROXY_MODE is properly set
env_check_output=$(sudo -u $UBUNTU_USER pm2 show hauling-qr-server 2>/dev/null || echo "")

if echo "$env_check_output" | grep -q "NGINX_PROXY_MODE.*true"; then
    log_success "✅ NGINX_PROXY_MODE=true confirmed in PM2"
else
    log_warning "⚠️ NGINX_PROXY_MODE not detected - applying manual fix..."
    
    # Manual fix: Restart PM2 with explicit environment variables
    log_fix "Restarting PM2 with explicit environment variables..."
    sudo -u $UBUNTU_USER pm2 delete hauling-qr-server 2>/dev/null || true
    sleep 2
    
    # Start with explicit environment variables
    sudo -u $UBUNTU_USER bash -c "cd '$APP_DIR' && NGINX_PROXY_MODE=true EXPRESS_CORS_DISABLED=true CORS_HANDLED_BY_NGINX=true NODE_ENV=production pm2 start ecosystem.config.js --env production"
    sleep 5
    
    # Save the configuration
    sudo -u $UBUNTU_USER pm2 save
    
    # Re-verify
    env_check_output=$(sudo -u $UBUNTU_USER pm2 show hauling-qr-server 2>/dev/null || echo "")
    if echo "$env_check_output" | grep -q "NGINX_PROXY_MODE.*true"; then
        log_success "✅ NGINX_PROXY_MODE manually fixed and confirmed"
    else
        log_error "❌ Failed to set NGINX_PROXY_MODE - manual intervention required"
        log_info "📝 Manual fix location: /var/www/hauling-qr-system/ecosystem.config.js"
        log_info "🔧 Required setting: NGINX_PROXY_MODE: 'true' in env_production section"
    fi
fi

# Step 6: Setup PM2 startup script for ubuntu user
log_step "6️⃣ Configuring PM2 startup script for ubuntu user..."

# Generate PM2 startup script
startup_cmd=$(sudo -u $UBUNTU_USER pm2 startup systemd -u $UBUNTU_USER --hp $UBUNTU_HOME 2>/dev/null | grep "sudo env" | head -1)

if [[ -n "$startup_cmd" ]]; then
    log_info "🔧 Installing PM2 startup script..."
    eval "$startup_cmd"
    log_success "✅ PM2 startup script installed for ubuntu user"
else
    log_warning "⚠️ Could not generate PM2 startup script"
fi

# Step 7: Final system verification
log_step "7️⃣ Final system verification and health checks..."

echo
echo "🔍 SYSTEM STATUS VERIFICATION:"
echo "================================"

# NGINX Status
echo "📊 NGINX Status:"
if sudo systemctl is-active nginx >/dev/null 2>&1; then
    log_success "✅ NGINX is running"
else
    log_error "❌ NGINX is not running"
fi

# PM2 Status
echo
echo "📊 PM2 Status:"
pm2_status=$(sudo -u $UBUNTU_USER pm2 list 2>/dev/null || echo "PM2 not accessible")
if echo "$pm2_status" | grep -q "hauling-qr-server.*online"; then
    log_success "✅ PM2 hauling-qr-server is online"

    # Show PM2 user
    pm2_user=$(ps aux | grep "PM2 v" | grep -v grep | awk '{print $1}' | head -1)
    if [[ "$pm2_user" == "$UBUNTU_USER" ]]; then
        log_success "✅ PM2 is running as ubuntu user (not root)"
    else
        log_warning "⚠️ PM2 user: $pm2_user (expected: $UBUNTU_USER)"
    fi
else
    log_error "❌ PM2 hauling-qr-server is not online"
fi

# Environment Variables Verification
echo
echo "📊 Environment Variables:"
env_output=$(sudo -u $UBUNTU_USER pm2 show hauling-qr-server 2>/dev/null || echo "")
if echo "$env_output" | grep -q "NGINX_PROXY_MODE.*true"; then
    log_success "✅ NGINX_PROXY_MODE=true confirmed"
else
    log_error "❌ NGINX_PROXY_MODE not set correctly"
fi

if echo "$env_output" | grep -q "NODE_ENV.*production"; then
    log_success "✅ NODE_ENV=production confirmed"
else
    log_warning "⚠️ NODE_ENV not set to production"
fi

# Backend Health Check
echo
echo "📊 Backend Health Check:"
if curl -f -s http://localhost:8080/api/health >/dev/null 2>&1; then
    log_success "✅ Backend is responding on port 8080"
else
    log_warning "⚠️ Backend health check failed (may be starting up)"
fi

# Database Connection Check
echo
echo "📊 Database Connection:"
if sudo -u postgres psql -d hauling_qr_system -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "✅ PostgreSQL database is accessible"
else
    log_warning "⚠️ PostgreSQL database connection issue"
fi

# Step 8: Performance and security optimizations
log_step "8️⃣ Applying final performance and security optimizations..."

# Set proper file permissions
log_info "🔒 Setting secure file permissions..."
sudo chown -R $UBUNTU_USER:$UBUNTU_USER "$APP_DIR"
sudo chmod -R 755 "$APP_DIR"
sudo chmod 600 "$APP_DIR"/.env* 2>/dev/null || true

# Optimize system limits for the ubuntu user
log_info "⚡ Optimizing system limits..."
sudo tee -a /etc/security/limits.conf >/dev/null <<EOF

# Hauling QR Trip System optimizations for ubuntu user
$UBUNTU_USER soft nofile 65536
$UBUNTU_USER hard nofile 65536
$UBUNTU_USER soft nproc 32768
$UBUNTU_USER hard nproc 32768
EOF

# Create logrotate configuration for PM2 logs
log_info "📝 Setting up log rotation..."
sudo tee /etc/logrotate.d/hauling-qr-pm2 >/dev/null <<EOF
/var/log/pm2/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 $UBUNTU_USER $UBUNTU_USER
    postrotate
        sudo -u $UBUNTU_USER pm2 reloadLogs
    endscript
}
EOF

log_success "✅ Performance and security optimizations applied"

# Final summary
echo
echo "🎉 FINAL TOUCH COMPLETED SUCCESSFULLY!"
echo "======================================"
echo
log_success "✅ NGINX optimized with single upstream (Error 520 prevention)"
log_success "✅ PM2 running as ubuntu user (not root)"
log_success "✅ NGINX_PROXY_MODE environment variable configured"
log_success "✅ Single instance PM2 configuration (prevents port conflicts)"
log_success "✅ CORS handled by NGINX (prevents duplicate headers)"
log_success "✅ System startup scripts configured"
log_success "✅ Performance and security optimizations applied"

echo
echo "🌐 Your Hauling QR Trip System is ready!"
echo "🔗 Test your site: https://$PRODUCTION_DOMAIN/"
echo
echo "📋 Key Improvements:"
echo "   • PM2 runs as ubuntu user (security best practice)"
echo "   • Single PM2 instance prevents Error 520/521"
echo "   • NGINX_PROXY_MODE properly configured"
echo "   • Optimized CORS handling"
echo "   • Enhanced logging and monitoring"
echo "   • Automatic startup on reboot"
echo
echo "🔧 Manual verification commands:"
echo "   sudo -u ubuntu pm2 status"
echo "   sudo -u ubuntu pm2 show hauling-qr-server"
echo "   sudo systemctl status nginx"
echo "   curl -I https://$PRODUCTION_DOMAIN/"
