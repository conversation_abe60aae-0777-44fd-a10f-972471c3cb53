#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SHARED CONFIGURATION MODULE
# =============================================================================
# Version: 1.0.0 - Centralized configuration for all deployment modules
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: Common variables, functions, and utilities for modular deployment
# =============================================================================

# Prevent multiple sourcing
if [[ "${SHARED_CONFIG_LOADED:-}" == "true" ]]; then
  return 0
fi
readonly SHARED_CONFIG_LOADED="true"

# =============================================================================
# CORE CONFIGURATION VARIABLES
# =============================================================================
# VERSION and LOG_DIR are set by the calling script (auto-deploy.sh)
readonly SHARED_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Set LOG_DIR with default if not already set by auto-deploy.sh
if [[ -z "${LOG_DIR:-}" ]]; then
  readonly LOG_DIR="/var/log/hauling-qr-deployment"
fi

# Only set these if not already set by auto-deploy.sh
if [[ -z "${GITHUB_REPO:-}" ]]; then
  readonly GITHUB_REPO="mightybadz18/hauling-qr-trip-management"
fi

# CRITICAL: Dynamic domain configuration - use environment variable with fallback
if [[ -z "${PRODUCTION_DOMAIN:-}" ]]; then
  readonly PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"
fi

# Application Configuration
readonly APP_NAME="hauling-qr-system"
readonly APP_DIR="/var/www/${APP_NAME}"
readonly DB_NAME="hauling_qr_system"
readonly DB_USER="postgres"
readonly DB_PASSWORD="PostgreSQLPassword123"
readonly JWT_SECRET="hauling_qr_jwt_secret_2025_secure_key_for_production"

# Network Configuration - CLOUDFLARE COMPATIBLE
readonly CLIENT_PORT=3000
readonly SERVER_HTTP_PORT=8080  # Cloudflare-compatible port
readonly SERVER_HTTPS_PORT=8443
readonly SERVER_PORT=8080  # Legacy support

# Production URLs - DYNAMIC for flexible deployment
readonly PRODUCTION_API_URL="https://api.${PRODUCTION_DOMAIN}/api"
readonly PRODUCTION_WS_URL="wss://api.${PRODUCTION_DOMAIN}/ws"
readonly PRODUCTION_FRONTEND_URL="https://${PRODUCTION_DOMAIN}"

# Ubuntu User Configuration
readonly UBUNTU_USER="ubuntu"
readonly UBUNTU_HOME="/home/<USER>"

# Environment Variables (can be overridden)
DETECTED_VPS_IP="${DETECTED_VPS_IP:-}"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-development}"
PRESERVE_ARTIFACTS="${PRESERVE_ARTIFACTS:-false}"
GITHUB_PAT="${GITHUB_PAT:-}"
GITHUB_USERNAME="${GITHUB_USERNAME:-}"
MANUAL_IP="${MANUAL_IP:-}"

# WSL Detection
readonly IS_WSL=$(grep -qi microsoft /proc/version && echo "true" || echo "false")

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Initialize logging directory
init_logging() {
  sudo mkdir -p "$LOG_DIR"
  sudo chmod 755 "$LOG_DIR"
  sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true
}

# Timestamp function
ts() { date '+%Y-%m-%d %H:%M:%S'; }

# Base logging function
log() { 
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo "[$(ts)] $*" | tee -a "$log_file"
}

# Colored logging functions
log_info() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$log_file"
}

log_success() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$log_file"
}

log_warning() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$log_file"
}

log_error() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$log_file"
}

log_debug() {
  local log_file="${LOG_FILE:-${LOG_DIR}/shared-$(date +%Y%m%d-%H%M%S).log}"
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$log_file"
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Check if running as root
check_root() {
  if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root (use sudo)"
    exit 1
  fi
}

# =============================================================================
# INTELLIGENT DETECTION FRAMEWORK
# =============================================================================

# Check if command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Advanced package detection with version checking
package_exists() {
  local package_name="$1"
  local min_version="${2:-}"

  # Check if package is installed
  if ! dpkg -s "$package_name" >/dev/null 2>&1; then
    return 1
  fi

  # If no version requirement, package exists
  if [[ -z "$min_version" ]]; then
    return 0
  fi

  # Version comparison (basic implementation)
  local installed_version
  installed_version=$(dpkg -s "$package_name" | grep '^Version:' | cut -d' ' -f2 | cut -d'-' -f1)

  if [[ -n "$installed_version" ]]; then
    # Simple version comparison (works for most cases)
    if dpkg --compare-versions "$installed_version" ge "$min_version"; then
      return 0
    fi
  fi

  return 1
}

# Service health validation
service_is_healthy() {
  local service_name="$1"
  local test_command="${2:-}"

  # Check if service is active
  if ! systemctl is-active --quiet "$service_name" 2>/dev/null; then
    return 1
  fi

  # Check if service is enabled
  if ! systemctl is-enabled --quiet "$service_name" 2>/dev/null; then
    return 1
  fi

  # Run optional test command
  if [[ -n "$test_command" ]]; then
    if ! eval "$test_command" >/dev/null 2>&1; then
      return 1
    fi
  fi

  return 0
}

# Node.js module validation
nodejs_module_exists() {
  local module_name="$1"
  local app_dir="${2:-$APP_DIR}"

  # Check in root node_modules
  if [[ -d "$app_dir/node_modules/$module_name" ]]; then
    # Test if module can be required
    if cd "$app_dir" && node -e "require('$module_name')" >/dev/null 2>&1; then
      return 0
    fi
  fi

  # Check in server node_modules (for symlinks)
  if [[ -d "$app_dir/server/node_modules/$module_name" ]]; then
    if cd "$app_dir/server" && node -e "require('$module_name')" >/dev/null 2>&1; then
      return 0
    fi
  fi

  return 1
}

# Database validation - STRICT PASSWORD AUTHENTICATION REQUIRED
database_is_healthy() {
  local db_name="$1"
  local db_user="$2"
  local db_password="$3"

  # Check if PostgreSQL is running (primary health check)
  if ! service_is_healthy "postgresql"; then
    return 1
  fi

  # CRITICAL: Test password authentication first - this is the main issue
  # Try connecting to postgres database with password (most basic test)
  if ! PGPASSWORD="$db_password" psql -h localhost -U "$db_user" -d "postgres" -c "SELECT 1;" >/dev/null 2>&1; then
    # Password authentication failed - this is the root cause of deployment failures
    return 1
  fi

  # Check if specific database exists and is accessible
  if PGPASSWORD="$db_password" psql -h localhost -U "$db_user" -d "$db_name" -c "SELECT 1;" >/dev/null 2>&1; then
    return 0
  fi

  # Database doesn't exist but password authentication works - this is acceptable
  # The database will be created during installation
  return 0
}

# NGINX configuration validation for Hauling QR Trip System
nginx_config_is_valid() {
  local domain="${1:-$PRODUCTION_DOMAIN}"

  # Check if nginx is installed and running
  if ! command_exists nginx || ! service_is_healthy "nginx" "nginx -t"; then
    return 1
  fi

  # Check if our site configuration exists
  local site_config="/etc/nginx/sites-available/hauling-qr-system"
  if [[ ! -f "$site_config" ]]; then
    return 1
  fi

  # Check if site is enabled
  local site_enabled="/etc/nginx/sites-enabled/hauling-qr-system"
  if [[ ! -L "$site_enabled" ]]; then
    return 1
  fi

  # Validate configuration contains required elements
  if grep -q "proxy_pass.*8080" "$site_config" && \
     grep -q "server_name.*$domain" "$site_config" && \
     grep -q "Access-Control-Allow-Origin" "$site_config"; then
    return 0
  fi

  return 1
}

# PM2 application validation
pm2_app_is_healthy() {
  local app_name="${1:-hauling-qr-server}"
  local expected_env="${2:-production}"

  # Check if PM2 is installed
  if ! command_exists pm2; then
    return 1
  fi

  # Check if application is running (primary health check)
  if pm2 list | grep -q "$app_name.*online"; then
    # If app is running, consider it healthy
    # Environment variables will be handled by setup-system-startup.sh
    return 0
  fi

  # If app is not running, check if PM2 is at least functional
  if pm2 list >/dev/null 2>&1; then
    # PM2 is functional but app is not running - this is acceptable for fresh installs
    # The app will be started by setup-system-startup.sh
    return 0
  fi

  return 1
}

# =============================================================================
# IDEMPOTENT MARKERS AND CACHING SYSTEM
# =============================================================================

# Markers directory
readonly MARKERS_DIR="/var/log/hauling-qr-deployment/phase-completion-markers"

# Initialize markers system
init_markers_system() {
  # Ensure parent log directory exists first
  local parent_dir="/var/log/hauling-qr-deployment"
  if [[ ! -d "$parent_dir" ]]; then
    sudo mkdir -p "$parent_dir"
    sudo chmod 755 "$parent_dir"
    sudo chown ubuntu:ubuntu "$parent_dir" 2>/dev/null || true
  fi

  # Create markers directory with robust error handling
  if [[ ! -d "$MARKERS_DIR" ]]; then
    if sudo mkdir -p "$MARKERS_DIR"; then
      sudo chmod 755 "$MARKERS_DIR"
      sudo chown ubuntu:ubuntu "$MARKERS_DIR" 2>/dev/null || true
      log_info "📁 Markers directory created: $MARKERS_DIR"
    else
      log_error "❌ Failed to create markers directory: $MARKERS_DIR"
      return 1
    fi
  fi

  return 0
}

# Create phase completion marker
create_phase_completion_marker() {
  local phase_name="$1"
  local validation_checksum="${2:-}"

  # Initialize markers system with error handling
  if ! init_markers_system; then
    log_error "❌ Failed to initialize markers system for phase: $phase_name"
    return 1
  fi

  local marker_file="$MARKERS_DIR/${phase_name}.marker"
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

  # Create marker file with proper permissions
  local temp_file="/tmp/marker_${phase_name}_$$"

  cat > "$temp_file" << EOF
PHASE_NAME=$phase_name
COMPLETION_TIME=$timestamp
VALIDATION_CHECKSUM=$validation_checksum
STATUS=completed
EOF

  # Move temp file to final location with proper permissions
  if sudo mv "$temp_file" "$marker_file"; then
    sudo chmod 644 "$marker_file"
    sudo chown ubuntu:ubuntu "$marker_file" 2>/dev/null || true
    log_info "📍 Phase completion marker created: $phase_name"
    return 0
  else
    log_error "❌ Failed to create phase completion marker: $phase_name"
    rm -f "$temp_file" 2>/dev/null || true
    return 1
  fi
}

# Check if phase was completed recently
phase_completed_recently() {
  local phase_name="$1"
  local max_age_hours="${2:-24}"

  # Ensure markers system is initialized
  if ! init_markers_system; then
    log_warning "⚠️ Cannot check phase completion - markers system unavailable"
    return 1
  fi

  local marker_file="$MARKERS_DIR/${phase_name}.marker"

  if [[ ! -f "$marker_file" ]]; then
    return 1
  fi

  # Check if marker is recent enough with error handling
  local marker_age_hours
  if marker_age_hours=$(( ($(date +%s) - $(stat -c %Y "$marker_file" 2>/dev/null || echo 0)) / 3600 )); then
    if [[ $marker_age_hours -le $max_age_hours ]]; then
      return 0
    fi
  else
    log_warning "⚠️ Cannot determine age of marker file: $marker_file"
    return 1
  fi

  return 1
}

# Timestamp-based caching for installations
installation_is_recent() {
  local installation_path="$1"
  local max_age_hours="${2:-24}"

  if [[ ! -d "$installation_path" ]]; then
    return 1
  fi

  # Check modification time
  local path_age_hours
  path_age_hours=$(( ($(date +%s) - $(stat -c %Y "$installation_path")) / 3600 ))

  if [[ $path_age_hours -le $max_age_hours ]]; then
    return 0
  fi

  return 1
}

# Validate installation integrity
validate_installation_integrity() {
  local installation_type="$1"
  local installation_path="$2"

  case "$installation_type" in
    "nodejs_modules")
      # Check if critical modules exist and are functional
      local critical_modules=("pg" "express" "cors" "helmet" "bcryptjs" "jsonwebtoken" "joi" "qrcode" "multer" "dotenv" "winston" "compression")
      for module in "${critical_modules[@]}"; do
        if ! nodejs_module_exists "$module" "$installation_path"; then
          return 1
        fi
      done
      return 0
      ;;
    "nginx_config")
      return $(nginx_config_is_valid)
      ;;
    "postgresql")
      return $(database_is_healthy "$DB_NAME" "$DB_USER" "$DB_PASSWORD")
      ;;
    "pm2_app")
      return $(pm2_app_is_healthy)
      ;;
    *)
      log_warning "⚠️ Unknown installation type for validation: $installation_type"
      return 1
      ;;
  esac
}

# Smart installation decision
should_skip_installation() {
  local phase_name="$1"
  local installation_type="$2"
  local installation_path="$3"
  local max_age_hours="${4:-24}"

  # Check if phase was completed recently
  if phase_completed_recently "$phase_name" "$max_age_hours"; then
    log_info "📍 Phase $phase_name completed recently, checking integrity..."

    # Validate installation integrity
    if validate_installation_integrity "$installation_type" "$installation_path"; then
      log_success "✅ $phase_name installation is recent and valid - SKIPPING"
      return 0
    else
      log_warning "⚠️ $phase_name installation is recent but invalid - REINSTALLING"
      return 1
    fi
  fi

  # Check if installation exists and is recent
  if installation_is_recent "$installation_path" "$max_age_hours"; then
    log_info "📍 $installation_type installation is recent, validating..."

    if validate_installation_integrity "$installation_type" "$installation_path"; then
      log_success "✅ $installation_type is recent and valid - SKIPPING"
      return 0
    else
      log_warning "⚠️ $installation_type is recent but invalid - REINSTALLING"
      return 1
    fi
  fi

  log_info "🔄 $phase_name requires installation/update"
  return 1
}

# =============================================================================
# PERFORMANCE MONITORING AND OPTIMIZATION
# =============================================================================

# Performance tracking
readonly PERF_LOG="$LOG_DIR/deployment-performance.log"

# Start performance timer
start_performance_timer() {
  local operation_name="$1"
  echo "$(date +%s):START:$operation_name" >> "$PERF_LOG"
}

# End performance timer
end_performance_timer() {
  local operation_name="$1"
  local start_time end_time duration

  # Find the start time for this operation
  start_time=$(grep ":START:$operation_name$" "$PERF_LOG" | tail -1 | cut -d: -f1)
  end_time=$(date +%s)

  if [[ -n "$start_time" ]]; then
    duration=$((end_time - start_time))
    echo "$end_time:END:$operation_name:${duration}s" >> "$PERF_LOG"
    log_info "⏱️ $operation_name completed in ${duration}s"
  fi
}

# Generate performance report
generate_performance_report() {
  if [[ ! -f "$PERF_LOG" ]]; then
    return 0
  fi

  log_info "📊 DEPLOYMENT PERFORMANCE REPORT:"

  local total_time=0
  while IFS=: read -r timestamp action operation duration; do
    if [[ "$action" == "END" && "$duration" =~ ^[0-9]+s$ ]]; then
      local seconds=${duration%s}
      total_time=$((total_time + seconds))
      log_info "   • $operation: ${duration}"
    fi
  done < "$PERF_LOG"

  log_info "   • TOTAL DEPLOYMENT TIME: ${total_time}s ($(($total_time / 60))m $(($total_time % 60))s)"
}

# Parallel execution helper
execute_parallel() {
  local -a commands=("$@")
  local -a pids=()
  local -a results=()

  # Start all commands in parallel
  for cmd in "${commands[@]}"; do
    eval "$cmd" &
    pids+=($!)
  done

  # Wait for all commands to complete
  local all_success=true
  for pid in "${pids[@]}"; do
    if wait "$pid"; then
      results+=(0)
    else
      results+=(1)
      all_success=false
    fi
  done

  # Return success only if all commands succeeded
  if $all_success; then
    return 0
  else
    return 1
  fi
}

# Resource usage monitoring
monitor_resource_usage() {
  local operation_name="$1"
  local duration="${2:-5}"

  log_info "📊 Monitoring resource usage for: $operation_name"

  # Monitor for specified duration
  for ((i=1; i<=duration; i++)); do
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1)

    echo "$(date +%s):RESOURCE:$operation_name:CPU:${cpu_usage}%:MEM:${mem_usage}%:DISK:${disk_usage}%" >> "$PERF_LOG"

    if [[ $i -lt $duration ]]; then
      sleep 1
    fi
  done
}

# Selective package installation
install_missing_packages() {
  local -a all_packages=("$@")
  local -a missing_packages=()

  log_info "🔍 Checking which packages need installation..."

  # Check each package
  for package in "${all_packages[@]}"; do
    if ! package_exists "$package"; then
      missing_packages+=("$package")
      log_info "   ❌ Missing: $package"
    else
      log_info "   ✅ Installed: $package"
    fi
  done

  # Install only missing packages
  if [[ ${#missing_packages[@]} -eq 0 ]]; then
    log_success "✅ All packages already installed - SKIPPING installation"
    return 0
  fi

  log_info "📦 Installing ${#missing_packages[@]} missing packages: ${missing_packages[*]}"

  # Update package lists first
  sudo apt-get update >/dev/null 2>&1 || true

  # Install missing packages
  if sudo DEBIAN_FRONTEND=noninteractive apt-get install -y "${missing_packages[@]}"; then
    log_success "✅ Successfully installed missing packages"
    return 0
  else
    log_error "❌ Failed to install some packages"
    return 1
  fi
}

# Wait for service to be ready
wait_for_service() {
  local service_name="$1"
  local max_attempts="${2:-30}"
  local attempt=1
  
  log_info "Waiting for $service_name to be ready..."
  
  while [[ $attempt -le $max_attempts ]]; do
    if systemctl is-active "$service_name" >/dev/null 2>&1; then
      log_success "✅ $service_name is ready"
      return 0
    fi
    
    log_info "Attempt $attempt/$max_attempts: $service_name not ready, waiting..."
    sleep 2
    attempt=$((attempt + 1))
  done
  
  log_error "❌ $service_name failed to become ready after $max_attempts attempts"
  return 1
}

# WSL-compatible service management
wsl_service_start() {
  local service_name="$1"
  local timeout="${2:-15}"
  
  log_info "Starting $service_name (WSL-compatible)..."
  
  # Method 1: Direct service command
  if timeout "$timeout" service "$service_name" start >/dev/null 2>&1; then
    log_success "✅ $service_name started via service command"
    return 0
  fi
  
  # Method 2: Init.d script
  if timeout "$timeout" /etc/init.d/"$service_name" start >/dev/null 2>&1; then
    log_success "✅ $service_name started via init.d"
    return 0
  fi
  
  # Method 3: Systemctl (if available)
  if command_exists systemctl && timeout "$timeout" systemctl start "$service_name" >/dev/null 2>&1; then
    log_success "✅ $service_name started via systemctl"
    return 0
  fi
  
  log_error "❌ Failed to start $service_name using all methods"
  return 1
}

# WSL-compatible service reload
wsl_service_reload() {
  local service_name="$1"
  local timeout="${2:-15}"
  
  log_info "Reloading $service_name (WSL-compatible)..."
  
  # Method 1: Direct service reload
  if timeout "$timeout" service "$service_name" reload >/dev/null 2>&1; then
    log_success "✅ $service_name reloaded via service command"
    return 0
  fi
  
  # Method 2: Service restart
  if timeout "$timeout" service "$service_name" restart >/dev/null 2>&1; then
    log_success "✅ $service_name restarted via service command"
    return 0
  fi
  
  # Method 3: Init.d restart
  if timeout "$timeout" /etc/init.d/"$service_name" restart >/dev/null 2>&1; then
    log_success "✅ $service_name restarted via init.d"
    return 0
  fi
  
  log_error "❌ Failed to reload $service_name using all methods"
  return 1
}

# Cleanup function for error handling
cleanup_on_error() {
  local script_name="${1:-unknown}"
  log_error "❌ Error occurred in $script_name, performing cleanup..."
  
  # Stop any running services that might be in inconsistent state
  service nginx stop >/dev/null 2>&1 || true
  service postgresql stop >/dev/null 2>&1 || true
  
  # Clean up temporary files
  rm -f /tmp/hauling-qr-* 2>/dev/null || true
  
  log_info "🧹 Cleanup completed for $script_name"
}

# Set up error handling for a script
setup_error_handling() {
  local script_name="$1"
  set -euo pipefail
  trap "cleanup_on_error '$script_name'" ERR
}

# =============================================================================
# NETWORK FUNCTIONS
# =============================================================================

# Detect VPS IP address
detect_vps_ip() {
  log_info "🔍 Detecting VPS IP address..."
  
  # Use manual IP if provided
  if [[ -n "$MANUAL_IP" ]]; then
    DETECTED_VPS_IP="$MANUAL_IP"
    log_success "✅ Using manual IP: $DETECTED_VPS_IP"
    return 0
  fi
  
  # Try multiple methods to detect IP
  local ip_methods=(
    "curl -s --connect-timeout 10 https://ipv4.icanhazip.com"
    "curl -s --connect-timeout 10 https://api.ipify.org"
    "curl -s --connect-timeout 10 https://checkip.amazonaws.com"
    "dig +short myip.opendns.com @resolver1.opendns.com"
  )
  
  for method in "${ip_methods[@]}"; do
    log_info "Trying: $method"
    if DETECTED_VPS_IP=$(eval "$method" 2>/dev/null | tr -d '\n\r' | grep -E '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$'); then
      if [[ -n "$DETECTED_VPS_IP" ]]; then
        log_success "✅ Detected VPS IP: $DETECTED_VPS_IP"
        return 0
      fi
    fi
  done
  
  log_error "❌ Failed to detect VPS IP address"
  return 1
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

# Validate environment variables
validate_environment() {
  log_info "🔍 Validating environment configuration..."
  
  local validation_errors=0
  
  # Check required directories
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check database credentials
  if [[ -z "$DB_PASSWORD" ]]; then
    log_error "❌ Database password not set"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check network configuration
  if [[ -z "$DETECTED_VPS_IP" ]]; then
    log_warning "⚠️ VPS IP not detected, some features may not work"
  fi
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ Environment validation passed"
    return 0
  else
    log_error "❌ Environment validation failed with $validation_errors errors"
    return 1
  fi
}

# =============================================================================
# PACKAGE MANAGER LOCK HANDLING FUNCTIONS
# =============================================================================

# Wait for package manager locks to clear
wait_for_package_manager_lock() {
  local max_wait_time=${1:-300}  # Default 5 minutes
  local check_interval=10
  local elapsed_time=0

  log_info "🔒 Checking for package manager locks..."

  while [[ $elapsed_time -lt $max_wait_time ]]; do
    local lock_files=(
      "/var/lib/dpkg/lock"
      "/var/lib/dpkg/lock-frontend"
      "/var/cache/apt/archives/lock"
      "/var/lib/apt/lists/lock"
    )

    local locks_found=0
    local blocking_processes=()

    # Check for lock files
    for lock_file in "${lock_files[@]}"; do
      if sudo fuser "$lock_file" >/dev/null 2>&1; then
        locks_found=$((locks_found + 1))
        local pids=$(sudo fuser "$lock_file" 2>/dev/null | tr -d ' ')
        if [[ -n "$pids" ]]; then
          for pid in $pids; do
            local process_info=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
            blocking_processes+=("PID:$pid ($process_info)")
          done
        fi
      fi
    done

    if [[ $locks_found -eq 0 ]]; then
      log_success "✅ Package manager locks cleared"
      return 0
    fi

    log_info "⏳ Package manager locked by: ${blocking_processes[*]}"
    log_info "   Waiting ${check_interval}s... (${elapsed_time}/${max_wait_time}s elapsed)"

    sleep $check_interval
    elapsed_time=$((elapsed_time + check_interval))
  done

  log_error "❌ Package manager still locked after ${max_wait_time}s"
  return 1
}

# Stop unattended-upgrades service temporarily
stop_unattended_upgrades() {
  log_info "🛑 Temporarily stopping unattended-upgrades service..."

  # Check if unattended-upgrades is running
  if systemctl is-active unattended-upgrades >/dev/null 2>&1; then
    log_info "Stopping unattended-upgrades service..."
    sudo systemctl stop unattended-upgrades || true

    # Wait for it to fully stop
    local wait_count=0
    while systemctl is-active unattended-upgrades >/dev/null 2>&1 && [[ $wait_count -lt 30 ]]; do
      sleep 2
      wait_count=$((wait_count + 1))
    done

    if systemctl is-active unattended-upgrades >/dev/null 2>&1; then
      log_warning "⚠️ unattended-upgrades still running after 60s"
    else
      log_success "✅ unattended-upgrades stopped"
    fi
  fi

  # Also check for apt-daily services
  for service in apt-daily apt-daily-upgrade; do
    if systemctl is-active "$service" >/dev/null 2>&1; then
      log_info "Stopping $service service..."
      sudo systemctl stop "$service" || true
    fi
  done
}

# Restart unattended-upgrades service
restart_unattended_upgrades() {
  log_info "🔄 Restarting unattended-upgrades service..."
  sudo systemctl start unattended-upgrades || true
  log_success "✅ unattended-upgrades restarted"
}

# Safe apt-get wrapper with lock handling
safe_apt_get() {
  local operation="$1"
  shift
  local packages=("$@")

  log_info "🔧 Preparing safe apt-get $operation for: ${packages[*]}"

  # Stop unattended-upgrades
  stop_unattended_upgrades

  # Wait for locks to clear
  if ! wait_for_package_manager_lock 300; then
    log_error "❌ Cannot proceed - package manager is locked"
    restart_unattended_upgrades
    return 1
  fi

  # Execute the apt-get command
  local success=false
  if sudo apt-get "$operation" -y --no-install-recommends \
    -o Dpkg::Options::="--force-confdef" \
    -o Dpkg::Options::="--force-confold" \
    -o APT::Get::Assume-Yes=true \
    "${packages[@]}" 2>&1; then
    success=true
    log_success "✅ apt-get $operation completed successfully"
  else
    log_error "❌ apt-get $operation failed"
  fi

  # Restart unattended-upgrades
  restart_unattended_upgrades

  [[ "$success" == "true" ]] && return 0 || return 1
}

# =============================================================================
# INITIALIZATION
# =============================================================================

# Initialize shared configuration
init_shared_config() {
  log_info "🔧 Initializing shared configuration..."
  
  # Initialize logging
  init_logging
  
  # Detect environment
  if [[ "$IS_WSL" == "true" ]]; then
    log_info "🐧 WSL environment detected"
  else
    log_info "🐧 Native Linux environment detected"
  fi
  
  # Set deployment environment if not specified
  if [[ -z "$DEPLOYMENT_ENV" ]]; then
    if [[ -n "$PRODUCTION_DOMAIN" ]] && [[ "$DETECTED_VPS_IP" != "" ]]; then
      DEPLOYMENT_ENV="production"
    else
      DEPLOYMENT_ENV="development"
    fi
  fi
  
  log_info "🏗️ Deployment environment: $DEPLOYMENT_ENV"
  log_success "✅ Shared configuration initialized"
}

# =============================================================================
# REPOSITORY AUTHENTICATION HELPER
# =============================================================================
compose_auth_repo_url() {
  local url="$1"
  local pat="$2"
  # If no PAT, return original URL
  if [[ -z "$pat" ]]; then echo "$url"; return; fi
  # For GitHub HTTPS URLs, always use x-access-token format for compatibility
  # This works for both classic PATs and fine-grained PATs (github_pat_*)
  if [[ "$url" =~ ^https://github.com/ ]]; then
    echo "https://x-access-token:${pat}@github.com/${url#https://github.com/}"
  else
    echo "$url"
  fi
}

# Export important variables for child processes
export APP_DIR DB_NAME DB_USER DB_PASSWORD
export SERVER_HTTP_PORT CLIENT_PORT UBUNTU_USER
export DEPLOYMENT_ENV DETECTED_VPS_IP IS_WSL
export GITHUB_PAT GITHUB_REPO
export PRODUCTION_API_URL PRODUCTION_WS_URL PRODUCTION_FRONTEND_URL

# Initialize when sourced
init_shared_config
