#!/bin/bash

# =============================================================================
# Hauling QR System - Fix Permissions and Ubuntu User Setup
# =============================================================================
# This script fixes file permissions and ensures the application runs as 
# ubuntu user instead of root, fixing PM2 monitoring issues after restart.
#
# Usage: sudo ./fix-permissions-ubuntu-user.sh
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/hauling-qr-system"
UBUNTU_USER="ubuntu"
UBUNTU_HOME="/home/<USER>"
ECOSYSTEM_FILE="$APP_DIR/ecosystem.config.js"

echo -e "${BLUE}🔧 Hauling QR System - Permission Fix Script${NC}"
echo -e "${BLUE}=============================================${NC}"
echo ""

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check ubuntu user
if ! id "$UBUNTU_USER" &>/dev/null; then
    print_error "Ubuntu user does not exist"
    exit 1
fi

# Check app directory
if [[ ! -d "$APP_DIR" ]]; then
    print_error "Application directory $APP_DIR does not exist"
    exit 1
fi

echo -e "${YELLOW}🛑 Step 1: Stopping all processes${NC}"
pkill -f "node.*server" 2>/dev/null || true
sudo -u $UBUNTU_USER pm2 kill 2>/dev/null || true
pm2 kill 2>/dev/null || true
fuser -k 8080/tcp 2>/dev/null || true
print_status "All processes stopped"

echo ""
echo -e "${YELLOW}🔧 Step 2: Fixing file permissions${NC}"
chown -R $UBUNTU_USER:$UBUNTU_USER $APP_DIR
chmod -R 755 $APP_DIR
print_status "Application directory ownership fixed"

if [[ -f "$APP_DIR/server/server.js" ]]; then
    chmod +x $APP_DIR/server/server.js
    print_status "server.js made executable"
fi

if [[ -d "$UBUNTU_HOME/.pm2" ]]; then
    chown -R $UBUNTU_USER:$UBUNTU_USER $UBUNTU_HOME/.pm2
    chmod -R 755 $UBUNTU_HOME/.pm2
    print_status "Fixed PM2 directory permissions"
else
    sudo -u $UBUNTU_USER mkdir -p $UBUNTU_HOME/.pm2/logs
    chown -R $UBUNTU_USER:$UBUNTU_USER $UBUNTU_HOME/.pm2
    print_status "Created PM2 directory"
fi

for dir in "$APP_DIR/node_modules" "$APP_DIR/server/node_modules" "$APP_DIR/client/node_modules"; do
    if [[ -d "$dir" ]]; then
        chown -R $UBUNTU_USER:$UBUNTU_USER $dir
        print_status "Fixed ownership of $dir"
    fi
done

echo ""
echo -e "${YELLOW}🚀 Step 3: Starting PM2 as ubuntu user${NC}"
systemctl disable pm2-root 2>/dev/null || true
rm -f /etc/systemd/system/pm2-root.service 2>/dev/null || true

cd $APP_DIR
sudo -u $UBUNTU_USER pm2 kill >/dev/null 2>&1 || true

if [[ -f "$ECOSYSTEM_FILE" ]]; then
    print_status "Found ecosystem.config.js - starting PM2..."
    sudo -u $UBUNTU_USER pm2 start ecosystem.config.js --env production || print_warning "PM2 failed to start - check config"
    sudo -u $UBUNTU_USER pm2 save
    STARTUP_CMD=$(sudo -u $UBUNTU_USER pm2 startup systemd -u $UBUNTU_USER --hp $UBUNTU_HOME | grep "sudo env" | head -1)
    if [[ -n "$STARTUP_CMD" ]]; then
        eval $STARTUP_CMD
        print_status "PM2 startup script installed"
    fi
else
    print_warning "ecosystem.config.js not found - skipping PM2 start"
fi

echo ""
echo -e "${YELLOW}🔍 Step 4: Verification${NC}"
PROCESS_USER=$(ps aux | grep "node.*server" | grep -v grep | awk '{print $1}' | head -1)
if [[ "$PROCESS_USER" == "$UBUNTU_USER" ]]; then
    print_status "Node.js process is running as ubuntu user ✓"
else
    print_warning "Node.js process owner: $PROCESS_USER (should be ubuntu)"
fi

sudo -u $UBUNTU_USER pm2 status
if curl -f http://localhost:8080/health >/dev/null 2>&1; then
    print_status "Application is responding ✓"
else
    print_warning "Application health check failed"
fi

APP_OWNER=$(stat -c '%U' $APP_DIR)
if [[ "$APP_OWNER" == "$UBUNTU_USER" ]]; then
    print_status "Application directory owned by ubuntu ✓"
else
    print_warning "Application directory owner: $APP_OWNER (should be ubuntu)"
fi

echo ""
echo -e "${GREEN}🎉 Permission fix completed!${NC}"
