#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - COMPLETE POSTGRESQL INSTALLATION MODULE
# =============================================================================
# Version: 2.0.0 - Complete PostgreSQL installation with database setup and migrations
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: PostgreSQL 17.x installation, database initialization, and migration execution
# =============================================================================

# Source shared configuration and intelligent deployment framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/shared-config.sh"

# Load intelligent deployment framework
readonly INTELLIGENT_FRAMEWORK="${SCRIPT_DIR}/intelligent-deployment-framework.sh"
if [[ -f "$INTELLIGENT_FRAMEWORK" ]]; then
  source "$INTELLIGENT_FRAMEWORK"
else
  echo "⚠️ WARNING: Intelligent deployment framework not found: $INTELLIGENT_FRAMEWORK"
  # Define stub functions to prevent errors
  start_performance_timer() { echo "⏱️ Started timer: $1"; }
  end_performance_timer() { echo "⏱️ Completed timer: $1"; }
  generate_performance_report() { echo "📊 Performance report not available"; }
fi

# Module-specific configuration
readonly MODULE_NAME="install-postgresql"
readonly LOG_FILE="${LOG_DIR}/postgresql-install-$(date +%Y%m%d-%H%M%S).log"

# Setup error handling
setup_error_handling "$MODULE_NAME"

# =============================================================================
# PACKAGE MANAGER LOCK HANDLING FUNCTIONS
# =============================================================================
wait_for_package_manager_lock() {
  local max_wait_time=${1:-300}  # Default 5 minutes
  local check_interval=10
  local elapsed_time=0
  local aggressive_mode=false

  log_info "🔒 Checking for package manager locks..."

  while [[ $elapsed_time -lt $max_wait_time ]]; do
    local lock_files=(
      "/var/lib/dpkg/lock"
      "/var/lib/dpkg/lock-frontend"
      "/var/cache/apt/archives/lock"
      "/var/lib/apt/lists/lock"
    )

    local locks_found=0
    local blocking_processes=()

    # Check for lock files
    for lock_file in "${lock_files[@]}"; do
      if sudo fuser "$lock_file" >/dev/null 2>&1; then
        locks_found=$((locks_found + 1))
        local pids=$(sudo fuser "$lock_file" 2>/dev/null | tr -d ' ')
        if [[ -n "$pids" ]]; then
          for pid in $pids; do
            local process_info=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
            blocking_processes+=("PID:$pid ($process_info)")
          done
        fi
      fi
    done

    if [[ $locks_found -eq 0 ]]; then
      log_success "✅ Package manager locks cleared"
      return 0
    fi

    log_info "⏳ Package manager locked by: ${blocking_processes[*]}"
    log_info "   Waiting ${check_interval}s... (${elapsed_time}/${max_wait_time}s elapsed)"

    # After 2 minutes, become more aggressive
    if [[ $elapsed_time -gt 120 && "$aggressive_mode" == "false" ]]; then
      log_warning "⚠️ Enabling aggressive lock clearing after 2 minutes..."
      aggressive_mode=true

      # Try to kill unattended-upgrades processes
      sudo pkill -f unattended-upgrade 2>/dev/null || true
      sudo pkill -f apt-daily 2>/dev/null || true
      sudo pkill -f dpkg 2>/dev/null || true

      # Wait a bit for processes to die
      sleep 5
      continue
    fi

    sleep $check_interval
    elapsed_time=$((elapsed_time + check_interval))
  done

  log_error "❌ Package manager still locked after ${max_wait_time}s"
  log_error "   Blocking processes: ${blocking_processes[*]}"
  log_error "   Manual intervention: sudo killall unattended-upgrade apt apt-get dpkg"
  return 1
}

stop_unattended_upgrades() {
  log_info "🛑 Temporarily stopping unattended-upgrades service..."

  # Check if unattended-upgrades is running
  if systemctl is-active unattended-upgrades >/dev/null 2>&1; then
    log_info "Stopping unattended-upgrades service..."
    sudo systemctl stop unattended-upgrades || true

    # Wait for it to fully stop
    local wait_count=0
    while systemctl is-active unattended-upgrades >/dev/null 2>&1 && [[ $wait_count -lt 30 ]]; do
      sleep 2
      wait_count=$((wait_count + 1))
    done

    if systemctl is-active unattended-upgrades >/dev/null 2>&1; then
      log_warning "⚠️ unattended-upgrades still running after 60s"
    else
      log_success "✅ unattended-upgrades stopped"
    fi
  fi

  # Also check for apt-daily services
  for service in apt-daily apt-daily-upgrade; do
    if systemctl is-active "$service" >/dev/null 2>&1; then
      log_info "Stopping $service service..."
      sudo systemctl stop "$service" || true
    fi
  done
}

restart_unattended_upgrades() {
  log_info "🔄 Restarting unattended-upgrades service..."
  sudo systemctl start unattended-upgrades || true
  log_success "✅ unattended-upgrades restarted"
}

# =============================================================================
# INTELLIGENT POSTGRESQL INSTALLATION FUNCTIONS
# =============================================================================

# Intelligent PostgreSQL installation with health validation
install_postgresql_intelligent() {
  log_info "🧠 Analyzing PostgreSQL installation with intelligent detection..."

  # Check if PostgreSQL is already installed and healthy
  if command_exists psql && service_is_healthy "postgresql"; then
    local existing_version
    existing_version=$(sudo -u postgres psql --version 2>/dev/null | grep -oP '\d+\.\d+' | head -1 || echo "0")

    log_info "🗄️ PostgreSQL detected: version $existing_version"

    # Check if version meets requirements (12.0+)
    if [[ $(echo "$existing_version >= 12.0" | bc -l 2>/dev/null || echo "0") -eq 1 ]]; then
      # Test database connectivity and functionality
      if database_is_healthy "$DB_NAME" "$DB_USER" "$DB_PASSWORD"; then
        log_success "✅ PostgreSQL $existing_version is healthy and functional - SKIPPING installation"
        create_phase_completion_marker "postgresql-installation" "healthy-v$existing_version"
        return 0
      else
        log_warning "⚠️ PostgreSQL $existing_version exists but database is not healthy - RECONFIGURING"

        # Try to fix database issues without reinstalling
        if fix_postgresql_database_issues; then
          create_phase_completion_marker "postgresql-installation" "fixed-v$existing_version"
          return 0
        else
          log_warning "⚠️ Database issues could not be fixed - PROCEEDING with configuration"
        fi
      fi
    else
      log_warning "⚠️ PostgreSQL $existing_version is below recommended version 12.0 - UPGRADING"
    fi
  else
    log_info "🔄 PostgreSQL not found or not healthy - INSTALLING"
  fi

  # Proceed with installation using existing function
  if install_postgresql; then
    create_phase_completion_marker "postgresql-installation" "fresh-install-$(date +%s)"
    return 0
  else
    return 1
  fi
}

# Fix PostgreSQL database issues without reinstalling
fix_postgresql_database_issues() {
  log_info "🔧 Attempting to fix PostgreSQL database issues..."

  # Start PostgreSQL service if not running
  if ! service_is_healthy "postgresql"; then
    log_info "Starting PostgreSQL service..."
    if sudo systemctl start postgresql 2>/dev/null; then
      sleep 5
    else
      log_warning "⚠️ Could not start PostgreSQL service"
      return 1
    fi
  fi

  # CRITICAL FIX: Always set postgres user password first
  log_info "🔑 Setting postgres user password (critical fix)..."
  if sudo -u postgres psql -c "ALTER USER postgres PASSWORD '${DB_PASSWORD}';" 2>/dev/null; then
    log_success "✅ Postgres user password set successfully"
  else
    log_error "❌ Failed to set postgres user password"
    return 1
  fi

  # Check if database exists
  if ! sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    log_info "Database $DB_NAME does not exist - will be created during configuration"
    # Create the database since we're fixing issues
    log_info "Creating $DB_NAME database..."
    if sudo -u postgres psql -c "CREATE DATABASE $DB_NAME;" 2>/dev/null; then
      log_success "✅ Database $DB_NAME created successfully"
    else
      log_warning "⚠️ Could not create database, will try during configuration"
    fi
  fi

  # Test database connectivity with the password we just set
  log_info "🔍 Testing database connectivity with new password..."
  if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "✅ Database connectivity test passed"
    return 0
  else
    # Try connecting to postgres database instead (fallback)
    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -U "$DB_USER" -d "postgres" -c "SELECT 1;" >/dev/null 2>&1; then
      log_success "✅ Database connectivity test passed (using postgres database)"
      return 0
    else
      log_error "❌ Database connectivity test failed even after password fix"
      return 1
    fi
  fi
}

# =============================================================================
# LEGACY POSTGRESQL INSTALLATION FUNCTIONS
# =============================================================================
install_postgresql() {
  log_info "🗄️ Installing PostgreSQL 17.x for database compatibility..."

  # Check if PostgreSQL is already installed
  if command_exists psql; then
    local existing_version
    existing_version=$(sudo -u postgres psql --version 2>/dev/null | grep -oP '\d+\.\d+' | head -1 || echo "unknown")
    log_info "PostgreSQL already installed: version $existing_version"

    if [[ $(echo "$existing_version >= 17.0" | bc -l 2>/dev/null || echo "0") -eq 1 ]]; then
      log_success "✅ PostgreSQL $existing_version meets requirements (17.0+)"
      return 0
    else
      log_warning "⚠️ PostgreSQL $existing_version is below required version 17.0, upgrading..."
    fi
  fi

  # CRITICAL FIX: Handle package manager locks before any apt operations
  log_info "🔧 Preparing package manager for PostgreSQL installation..."

  # Stop unattended-upgrades to prevent lock conflicts
  stop_unattended_upgrades

  # Wait for any existing locks to clear
  if ! wait_for_package_manager_lock 300; then
    log_error "❌ Cannot proceed - package manager is locked"
    log_error "   Manual intervention required: sudo killall unattended-upgrade apt apt-get dpkg"
    return 1
  fi

  # Add PostgreSQL 17 official repository
  log_info "Adding PostgreSQL 17 official repository..."
  wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add - >/dev/null 2>&1 || {
    log_warning "⚠️ Failed to add PostgreSQL GPG key, trying alternative method..."
    curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg >/dev/null 2>&1
  }

  echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list >/dev/null 2>&1

  # PERFORMANCE OPTIMIZATION: PostgreSQL installation with timeout and progress
  local POSTGRES_INSTALL_TIMEOUT=600  # 10 minutes maximum
  local start_time=$(date +%s)

  log_info "📦 Updating package list for PostgreSQL 17 (timeout: ${POSTGRES_INSTALL_TIMEOUT}s)..."

  # Pre-installation optimization with lock checking
  if ! wait_for_package_manager_lock 60; then
    log_error "Package manager locked during update phase"
    return 1
  fi

  sudo apt-get update -y --fix-missing >/dev/null 2>&1 || {
    log_error "Failed to update package list for PostgreSQL 17"
    return 1
  }

  # Install essential packages first for faster dependency resolution
  if ! wait_for_package_manager_lock 60; then
    log_error "Package manager locked during transport packages installation"
    return 1
  fi

  sudo apt-get install -y --no-install-recommends apt-transport-https ca-certificates >/dev/null 2>&1 || {
    log_warning "⚠️ Failed to install transport packages, continuing..."
  }

  log_info "🚀 Installing PostgreSQL 17 with performance optimizations..."
  log_info "   Target: Complete within 10 minutes (600s)"

  # Final lock check before installation
  if ! wait_for_package_manager_lock 60; then
    log_error "Package manager locked during PostgreSQL installation"
    return 1
  fi

  # Optimized PostgreSQL installation with timeout and progress reporting
  local install_success=false
  local install_attempts=0
  local max_install_attempts=3

  while [[ $install_attempts -lt $max_install_attempts ]] && [[ "$install_success" == "false" ]]; do
    install_attempts=$((install_attempts + 1))
    log_info "PostgreSQL installation attempt $install_attempts/$max_install_attempts..."

    if timeout $POSTGRES_INSTALL_TIMEOUT sudo apt-get install -y --no-install-recommends \
      -o Dpkg::Options::="--force-confdef" \
      -o Dpkg::Options::="--force-confold" \
      -o APT::Get::Assume-Yes=true \
      postgresql-17 postgresql-client-17 postgresql-contrib-17 libpq-dev 2>&1 | \
      while IFS= read -r line; do
        echo "   📥 $line" | head -c 100
        echo ""
      done; then

      install_success=true
      local end_time=$(date +%s)
      local duration=$((end_time - start_time))
      log_success "✅ PostgreSQL 17 installed in ${duration}s (target: <600s)"

    else
      local end_time=$(date +%s)
      local duration=$((end_time - start_time))
      log_warning "⚠️ PostgreSQL installation attempt $install_attempts failed after ${duration}s"

      if [[ $install_attempts -lt $max_install_attempts ]]; then
        log_info "Waiting 30s before retry..."
        sleep 30

        # Check for locks again before retry
        if ! wait_for_package_manager_lock 120; then
          log_error "Package manager still locked, cannot retry"
          break
        fi
      fi
    fi
  done

  if [[ "$install_success" == "false" ]]; then
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    log_error "❌ PostgreSQL installation failed after $install_attempts attempts"
    log_error "   Total duration: ${duration}s (target: <600s)"

    # Restart unattended-upgrades before returning
    restart_unattended_upgrades
    return 1
  fi

  # Ensure PostgreSQL is enabled and started
  sudo systemctl enable postgresql >/dev/null 2>&1 || true
  sudo systemctl start postgresql >/dev/null 2>&1 || true

  # Restart unattended-upgrades now that installation is complete
  restart_unattended_upgrades

  log_success "✅ PostgreSQL 17.x installed successfully"
  return 0
}

validate_postgresql_version() {
  log_info "🔍 Validating PostgreSQL installation..."

  # Validate PostgreSQL 17.0+ is installed
  if ! command_exists psql; then
    log_error "PostgreSQL not found. Installation may have failed."
    return 1
  fi

  # Version validation logic
  local PG_VERSION
  PG_VERSION=$(sudo -u postgres psql --version 2>/dev/null | grep -oP '\d+\.\d+' | head -1)
  if [[ -z "$PG_VERSION" ]]; then
    log_error "Could not determine PostgreSQL version"
    return 1
  fi

  log_info "Found PostgreSQL version: $PG_VERSION"
  if [[ $(echo "$PG_VERSION >= 17.0" | bc -l) -eq 0 ]]; then
    log_error "PostgreSQL 17.0+ required for database compatibility, found $PG_VERSION"
    log_error "The database/init.sql was dumped from PostgreSQL 17.4 and requires version 17.0+"
    return 1
  fi

  log_success "✅ PostgreSQL $PG_VERSION meets version requirements (17.0+)"
  return 0
}

configure_postgresql_authentication() {
  log_info "🔐 Configuring PostgreSQL authentication..."

  # Check if Phase 0 optimization has been applied to postgresql.conf
  local pg_config_file
  pg_config_file=$(sudo find /etc/postgresql -name "postgresql.conf" | head -1)

  if [[ -n "$pg_config_file" ]] && sudo grep -q "HAULING QR TRIP SYSTEM - PERFORMANCE OPTIMIZATIONS" "$pg_config_file" 2>/dev/null; then
    log_info "🎯 Phase 0 PostgreSQL performance optimizations detected - preserving settings"
    log_success "✅ PostgreSQL performance configuration preserved (2GB shared_buffers, 200 max_connections)"
  fi

  # Detect PostgreSQL version for config path
  local pg_version
  pg_version=$(dpkg -l | grep postgresql-[0-9] | head -1 | grep -oP 'postgresql-\K[0-9]+' || echo "17")

  # Configure PostgreSQL authentication
  local pg_hba_file="/etc/postgresql/$pg_version/main/pg_hba.conf"
  if [[ -f "$pg_hba_file" ]]; then
    cp "$pg_hba_file" "$pg_hba_file.backup" 2>/dev/null || true

    # Configure authentication methods
    sed -i 's/local   all             postgres                                peer/local   all             postgres                                trust/' "$pg_hba_file"
    sed -i 's/local   all             all                                     peer/local   all             all                                     md5/' "$pg_hba_file"
    sed -i 's/host    all             all             127.0.0.1\/32            scram-sha-256/host    all             all             127.0.0.1\/32            md5/' "$pg_hba_file"
    sed -i 's/host    all             all             ::1\/128                 scram-sha-256/host    all             all             ::1\/128                 md5/' "$pg_hba_file"

    # Reload PostgreSQL configuration
    systemctl reload postgresql 2>/dev/null || true
    sleep 3

    log_success "✅ PostgreSQL authentication configured"
  else
    log_warning "⚠️ pg_hba.conf not found at expected location: $pg_hba_file"
    return 1
  fi

  return 0
}

setup_database() {
  log_info "🗄️ Setting up PostgreSQL database..."

  # Drop existing database if it exists
  if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    log_info "Dropping existing $DB_NAME database..."

    # Step 1: Prevent new connections
    log_info "Preventing new connections to $DB_NAME..."
    sudo -u postgres psql -c "UPDATE pg_database SET datallowconn = 'false' WHERE datname = '$DB_NAME';" || true

    # Step 2: Terminate all active connections (multiple attempts)
    for attempt in {1..3}; do
      log_info "Terminating active connections to $DB_NAME (attempt $attempt/3)..."

      # Get active connection count
      local active_connections=$(sudo -u postgres psql -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();" | tr -d ' ')

      if [[ "$active_connections" == "0" ]]; then
        log_info "✅ No active connections found"
        break
      fi

      log_info "Found $active_connections active connections, terminating..."

      # Terminate connections
      sudo -u postgres psql -c "
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = '$DB_NAME'
          AND pid <> pg_backend_pid();
      " || true

      # Wait longer between attempts
      sleep $((attempt * 2))
    done

    # Step 3: Final connection check and drop
    local remaining_connections=$(sudo -u postgres psql -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();" | tr -d ' ')

    if [[ "$remaining_connections" != "0" ]]; then
      log_warning "⚠️ Still $remaining_connections active connections, attempting force drop..."

      # Nuclear option: restart PostgreSQL to clear all connections
      log_info "Restarting PostgreSQL to clear all connections..."
      sudo systemctl restart postgresql || true
      sleep 5
    fi

    # Now drop the database
    if sudo -u postgres psql -c "DROP DATABASE IF EXISTS $DB_NAME;"; then
      log_success "✅ Database $DB_NAME dropped successfully"
    else
      log_error "❌ Failed to drop database $DB_NAME even after connection termination"
      log_info "Attempting to continue with existing database..."
      # Don't return 1 here, let it continue and try to work with existing database
    fi
  fi

  # Set postgres user password
  log_info "Setting postgres user password..."
  sudo -u postgres psql -c "ALTER USER postgres PASSWORD '${DB_PASSWORD}';" || {
    log_error "Failed to set postgres user password"
    return 1
  }

  # Create fresh database
  log_info "Creating fresh $DB_NAME database..."
  sudo -u postgres psql -c "CREATE DATABASE $DB_NAME;" || {
    log_error "Failed to create fresh database"
    return 1
  }

  # Test database connection
  log_info "Testing database connection..."
  if PGPASSWORD="${DB_PASSWORD}" psql -h localhost -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT 1;" >/dev/null 2>&1; then
    log_success "✅ Database connection successful"
  else
    log_error "❌ Database connection failed"
    return 1
  fi

  log_success "✅ Database setup completed"
  return 0
}

# =============================================================================
# DATABASE INITIALIZATION AND MIGRATIONS
# =============================================================================
initialize_database_with_migrations() {
  log_info "🗄️ Initializing database with migrations..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  if [[ ! -d "database" ]]; then
    log_error "❌ Database directory not found"
    return 1
  fi
  
  # Set up environment for database operations
  export DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}"
  
  # Step 1: Run database/init.sql first (if exists)
  if [[ -f "database/init.sql" ]]; then
    log_info "Step 1: Running database initialization (init.sql)..."
    if PGPASSWORD="${DB_PASSWORD}" psql -h localhost -U "${DB_USER}" -d "${DB_NAME}" -f database/init.sql 2>&1 | tee -a "$LOG_FILE"; then
      log_success "✅ Database initialization (init.sql) completed successfully"
    else
      log_error "❌ Database initialization (init.sql) failed"
      return 1
    fi
  else
    log_warning "⚠️ database/init.sql not found, skipping initialization"
  fi
  
  # Step 2: Update .env file with correct database credentials
  log_info "Step 2: Ensuring .env file has correct database credentials..."
  if [[ -f ".env" ]]; then
    # Remove any existing database-related lines to avoid conflicts
    sed -i '/^DB_PASSWORD=/d' .env
    sed -i '/^DB_HOST=/d' .env
    sed -i '/^DB_PORT=/d' .env
    sed -i '/^DB_NAME=/d' .env
    sed -i '/^DB_USER=/d' .env

    # Add the correct database configuration with proper quoting
    echo "DB_HOST=localhost" >> .env
    echo "DB_PORT=5432" >> .env
    echo "DB_NAME=${DB_NAME}" >> .env
    echo "DB_USER=${DB_USER}" >> .env
    echo "DB_PASSWORD=\"${DB_PASSWORD}\"" >> .env

    log_success "✅ .env file updated with database credentials"
  else
    log_warning "⚠️ .env file not found, database credentials not updated"
  fi
  
  # Step 3: Verify Node.js pg module is available
  log_info "Step 3: Verifying Node.js pg module..."
  if ! node -e "require('pg')" 2>/dev/null; then
    log_info "Installing pg module..."
    # Clean npm cache before pg installation
    sudo npm cache clean --force 2>/dev/null || true
    sudo npm install pg --save --force 2>&1 | tee -a "$LOG_FILE" || {
      log_error "❌ Failed to install pg module"
      return 1
    }
  fi
  log_success "✅ Node.js pg module verified"
  
  # Step 4: Run migrations using run-migration.js
  if [[ -f "database/run-migration.js" ]]; then
    log_info "Step 4: Running database migrations (run-migration.js)..."
    if node database/run-migration.js 2>&1 | tee -a "$LOG_FILE"; then
      log_success "✅ Database migrations completed successfully"

      # Verify database setup
      local trip_count
      trip_count=$(sudo -u postgres psql -d "${DB_NAME}" -t -c "SELECT COUNT(*) FROM trip_logs;" 2>/dev/null | xargs || echo "0")
      log_info "Database verification: ${trip_count} trips in database"

    else
      log_error "❌ Database migrations failed"
      return 1
    fi
  else
    log_warning "⚠️ database/run-migration.js not found, skipping migrations"
  fi
  
  # Step 5: Test database connection from Node.js
  log_info "Step 5: Testing database connection from Node.js..."
  if node -e "
    const { Pool } = require('pg');
    const pool = new Pool({
      user: '${DB_USER}',
      host: 'localhost',
      database: '${DB_NAME}',
      password: '${DB_PASSWORD}',
      port: 5432,
    });
    pool.query('SELECT NOW()', (err, res) => {
      if (err) {
        console.error('Database connection failed:', err);
        process.exit(1);
      } else {
        console.log('Database connection successful:', res.rows[0]);
        process.exit(0);
      }
      pool.end();
    });
  " 2>&1 | tee -a "$LOG_FILE"; then
    log_success "✅ Database connection test passed"
  else
    log_error "❌ Database connection test failed"
    return 1
  fi
  
  log_success "✅ Database initialization and migrations completed"
}

# =============================================================================
# MAIN INSTALLATION FUNCTION
# =============================================================================
main() {
  start_performance_timer "postgresql-installation-total"

  log_info "🚀 Starting Intelligent PostgreSQL Installation"
  log_info "📅 Started at: $(date)"
  log_info "📝 Log file: $LOG_FILE"
  log_info "🧠 Mode: Intelligent Detection with Database Health Validation"

  # Check root privileges
  check_root

  # Validate application directory exists
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    log_error "Please run setup-repository-environment.sh first"
    exit 1
  fi

  # INTELLIGENT PRE-INSTALLATION ANALYSIS
  log_info "🔍 Phase 0: Pre-Installation Database Analysis"
  start_performance_timer "database-analysis"

  # Check if we can skip this entire phase
  if should_skip_installation "postgresql-installation" "postgresql" "/var/lib/postgresql" 24; then
    log_success "✅ PostgreSQL installation is recent and valid - SKIPPING entire phase"
    end_performance_timer "database-analysis"
    end_performance_timer "postgresql-installation-total"
    return 0
  fi

  end_performance_timer "database-analysis"

  # Step 1: Intelligent PostgreSQL installation
  log_info "🗄️ Phase 1: PostgreSQL Installation (Intelligent)"
  start_performance_timer "postgresql-installation"

  if ! install_postgresql_intelligent; then
    log_error "❌ PostgreSQL installation failed"
    exit 1
  fi

  end_performance_timer "postgresql-installation"

  # Step 2: Intelligent PostgreSQL version validation
  log_info "✅ Phase 2: PostgreSQL Version Validation (Intelligent)"
  start_performance_timer "version-validation"

  if ! validate_postgresql_version; then
    log_error "❌ PostgreSQL version validation failed"
    exit 1
  fi

  end_performance_timer "version-validation"

  # Step 3: Intelligent PostgreSQL authentication configuration
  log_info "🔐 Phase 3: PostgreSQL Authentication (Intelligent)"
  start_performance_timer "authentication-config"

  if ! configure_postgresql_authentication; then
    log_error "❌ PostgreSQL authentication configuration failed"
    exit 1
  fi

  end_performance_timer "authentication-config"

  # Step 4: Intelligent database setup
  log_info "🗄️ Phase 4: Database Setup (Intelligent)"
  start_performance_timer "database-setup"

  if ! setup_database; then
    log_error "❌ Database setup failed"
    exit 1
  fi

  end_performance_timer "database-setup"

  # Step 5: Intelligent database initialization with migrations
  log_info "🔄 Phase 5: Database Initialization & Migrations (Intelligent)"
  start_performance_timer "database-migrations"

  if ! initialize_database_with_migrations; then
    log_error "❌ Database initialization and migrations failed"
    exit 1
  fi

  end_performance_timer "database-migrations"

  # Final comprehensive validation
  log_info "✅ Phase 6: Comprehensive PostgreSQL Validation"
  start_performance_timer "final-validation"

  local validation_errors=0
  local warnings=0

  # Service health validation
  log_info "🔍 Testing PostgreSQL service health..."
  if service_is_healthy "postgresql" "sudo -u postgres psql -c 'SELECT 1;'"; then
    log_info "   ✅ PostgreSQL service is healthy and functional"
  else
    log_error "   ❌ PostgreSQL service is not healthy"
    validation_errors=$((validation_errors + 1))
  fi

  # Database connectivity validation
  log_info "🔍 Testing database connectivity..."
  if database_is_healthy "$DB_NAME" "$DB_USER" "$DB_PASSWORD"; then
    log_info "   ✅ Database connectivity test passed"
  else
    log_error "   ❌ Database connectivity test failed"
    validation_errors=$((validation_errors + 1))
  fi

  # Database existence validation
  log_info "🔍 Validating database existence..."
  if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    log_info "   ✅ Database $DB_NAME exists"
  else
    log_error "   ❌ Database $DB_NAME does not exist"
    validation_errors=$((validation_errors + 1))
  fi

  # Node.js PostgreSQL module validation
  log_info "🔍 Testing Node.js PostgreSQL integration..."
  cd "$APP_DIR"
  if nodejs_module_exists "pg" "$APP_DIR"; then
    log_info "   ✅ Node.js pg module is functional"
  else
    log_warning "   ⚠️ Node.js pg module not available (may be installed later)"
    warnings=$((warnings + 1))
  fi

  end_performance_timer "final-validation"
  end_performance_timer "postgresql-installation-total"

  # Generate performance report
  generate_performance_report

  if [[ $validation_errors -eq 0 ]]; then
    log_success "🎉 Intelligent PostgreSQL Installation completed successfully!"

    if [[ $warnings -gt 0 ]]; then
      log_info "⚠️ Completed with $warnings warnings (non-critical)"
    fi

    log_info "📋 Installation Summary:"
    log_info "   ✅ PostgreSQL: Installed and running"
    log_info "   ✅ Database: $DB_NAME created and accessible"
    log_info "   ✅ Authentication: Configured and tested"
    log_info "   ✅ Migrations: Executed successfully"
    log_info "   ✅ Service Health: Validated and functional"

    # Create intelligent completion marker
    create_phase_completion_marker "postgresql-complete" "intelligent-install-$(date +%s)"

    return 0
  else
    log_error "❌ PostgreSQL Installation completed with $validation_errors errors"
    return 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
