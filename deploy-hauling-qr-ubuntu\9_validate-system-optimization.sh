#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SYSTEM OPTIMIZATION VALIDATION
# =============================================================================
# Validates that all system optimizations are correctly applied
# Provides detailed performance metrics and recommendations
# =============================================================================

set -euo pipefail

# Load shared configuration
readonly VALIDATION_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SHARED_CONFIG="${VALIDATION_SCRIPT_DIR}/shared-config.sh"

if [[ -f "$SHARED_CONFIG" ]]; then
  source "$SHARED_CONFIG"
else
  echo "❌ ERROR: Shared configuration not found: $SHARED_CONFIG"
  exit 1
fi

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

validate_postgresql_performance() {
  log_info "🐘 Validating PostgreSQL performance configuration..."
  
  local validation_errors=0
  
  # Check if PostgreSQL is running
  if ! sudo systemctl is-active --quiet postgresql; then
    log_error "❌ PostgreSQL service is not running"
    return 1
  fi
  
  # Test database connection
  if ! sudo -u postgres psql -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
    log_error "❌ Cannot connect to database: $DB_NAME"
    return 1
  fi
  
  # Check key performance parameters
  local params=(
    "shared_buffers:2GB"
    "effective_cache_size:6GB"
    "work_mem:16MB"
    "maintenance_work_mem:512MB"
    "max_connections:200"
    "wal_buffers:16MB"
  )
  
  for param in "${params[@]}"; do
    local key="${param%:*}"
    local expected="${param#*:}"
    local actual
    
    actual=$(sudo -u postgres psql -t -c "SHOW $key;" 2>/dev/null | xargs || echo "ERROR")
    
    if [[ "$actual" == "$expected" ]]; then
      log_success "✅ PostgreSQL $key: $actual"
    else
      log_warning "⚠️ PostgreSQL $key: $actual (expected: $expected)"
      validation_errors=$((validation_errors + 1))
    fi
  done
  
  # Check database performance statistics
  local active_connections
  active_connections=$(sudo -u postgres psql -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | xargs || echo "0")
  log_info "📊 Active database connections: $active_connections"
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ PostgreSQL performance validation passed"
    return 0
  else
    log_warning "⚠️ PostgreSQL performance validation completed with $validation_errors warnings"
    return 0
  fi
}

validate_pm2_cluster() {
  log_info "⚡ Validating PM2 cluster configuration..."
  
  # Check if PM2 is available
  if ! command -v pm2 >/dev/null 2>&1; then
    log_warning "⚠️ PM2 not found in PATH, checking if installed..."
    if ! npm list -g pm2 >/dev/null 2>&1; then
      log_error "❌ PM2 is not installed"
      return 1
    fi
  fi
  
  # Check ecosystem configuration
  if [[ ! -f "$APP_DIR/ecosystem.config.js" ]]; then
    log_error "❌ PM2 ecosystem configuration not found: $APP_DIR/ecosystem.config.js"
    return 1
  fi
  
  # CRITICAL FIX: Validate ecosystem configuration for Error 520 prevention (instances: 1)
  # The fix-production-issues.sh uses 1 instance to prevent port conflicts and Error 520
  if grep -q "instances: 1" "$APP_DIR/ecosystem.config.js" && \
     grep -q "exec_mode: 'cluster'" "$APP_DIR/ecosystem.config.js" && \
     grep -q "NGINX_PROXY_MODE.*true" "$APP_DIR/ecosystem.config.js"; then
    log_success "✅ PM2 ecosystem configuration validated (Error 520 prevention: instances=1, NGINX_PROXY_MODE=true)"
  else
    log_warning "⚠️ PM2 ecosystem configuration may not match Error 520 prevention settings"
    log_info "Expected: instances: 1, exec_mode: 'cluster', NGINX_PROXY_MODE: 'true'"
  fi
  
  # Check if application is running (if PM2 is active)
  if sudo -u "$UBUNTU_USER" pm2 list 2>/dev/null | grep -q "hauling-qr-server"; then
    local instances
    instances=$(sudo -u "$UBUNTU_USER" pm2 list 2>/dev/null | grep "hauling-qr-server" | wc -l)
    log_info "📊 PM2 instances running: $instances"
    
    if [[ $instances -eq 4 ]]; then
      log_success "✅ PM2 cluster running with optimal instance count"
    else
      log_warning "⚠️ PM2 cluster not running with expected 4 instances"
    fi
  else
    log_info "ℹ️ PM2 application not currently running (will be started in later phases)"
  fi
  
  return 0
}

validate_nginx_performance() {
  log_info "🌐 Validating NGINX performance configuration..."
  
  # Check NGINX configuration syntax
  if ! sudo nginx -t >/dev/null 2>&1; then
    log_error "❌ NGINX configuration has syntax errors"
    return 1
  fi
  
  # Check key performance parameters in nginx.conf
  local nginx_conf="/etc/nginx/nginx.conf"
  
  if [[ ! -f "$nginx_conf" ]]; then
    log_error "❌ NGINX configuration file not found: $nginx_conf"
    return 1
  fi
  
  local checks=(
    "worker_processes 4"
    "worker_connections 2048"
    "gzip_comp_level 6"
    "keepalive_timeout 65s"
  )
  
  local validation_errors=0
  for check in "${checks[@]}"; do
    if grep -q "$check" "$nginx_conf"; then
      log_success "✅ NGINX $check configured"
    else
      log_warning "⚠️ NGINX $check not found in configuration"
      validation_errors=$((validation_errors + 1))
    fi
  done
  
  # Check if NGINX is running
  if sudo systemctl is-active --quiet nginx; then
    log_success "✅ NGINX service is running"
  else
    log_warning "⚠️ NGINX service is not running"
  fi
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ NGINX performance validation passed"
  else
    log_warning "⚠️ NGINX performance validation completed with $validation_errors warnings"
  fi
  
  return 0
}

validate_system_performance() {
  log_info "🔧 Validating system performance parameters..."
  
  local validation_errors=0
  
  # Check kernel parameters
  local kernel_params=(
    "vm.swappiness:10"
    "vm.dirty_ratio:15"
    "vm.dirty_background_ratio:5"
    "fs.file-max:2097152"
    "net.core.somaxconn:65535"
  )
  
  for param in "${kernel_params[@]}"; do
    local key="${param%:*}"
    local expected="${param#*:}"
    local actual
    
    actual=$(sysctl -n "$key" 2>/dev/null || echo "ERROR")
    
    if [[ "$actual" == "$expected" ]]; then
      log_success "✅ Kernel parameter $key: $actual"
    else
      log_warning "⚠️ Kernel parameter $key: $actual (expected: $expected)"
      validation_errors=$((validation_errors + 1))
    fi
  done
  
  # Check memory information
  local total_mem_gb
  total_mem_gb=$(free -g | awk '/^Mem:/{print $2}')
  log_info "📊 Total system memory: ${total_mem_gb}GB"
  
  if [[ $total_mem_gb -ge 7 ]]; then
    log_success "✅ System memory meets requirements (${total_mem_gb}GB >= 7GB)"
  else
    log_warning "⚠️ System memory below optimal (${total_mem_gb}GB < 8GB)"
  fi
  
  # Check CPU information
  local cpu_cores
  cpu_cores=$(nproc)
  log_info "📊 CPU cores available: $cpu_cores"
  
  if [[ $cpu_cores -ge 4 ]]; then
    log_success "✅ CPU cores meet requirements ($cpu_cores >= 4)"
  else
    log_warning "⚠️ CPU cores below optimal ($cpu_cores < 4)"
  fi
  
  # Check swap configuration
  local swap_total
  swap_total=$(free -h | awk '/^Swap:/{print $2}')
  log_info "📊 Swap space configured: $swap_total"
  
  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ System performance validation passed"
  else
    log_warning "⚠️ System performance validation completed with $validation_errors warnings"
  fi
  
  return 0
}

generate_performance_report() {
  log_info "📊 Generating system performance report..."
  
  local report_file="/tmp/hauling-qr-performance-report.txt"
  
  cat > "$report_file" << EOF
# =============================================================================
# HAULING QR TRIP SYSTEM - PERFORMANCE OPTIMIZATION REPORT
# =============================================================================
# Generated: $(date)
# System: $(uname -a)
# =============================================================================

## System Resources
- CPU Cores: $(nproc)
- Total Memory: $(free -h | awk '/^Mem:/{print $2}')
- Available Memory: $(free -h | awk '/^Mem:/{print $7}')
- Swap Space: $(free -h | awk '/^Swap:/{print $2}')
- Disk Space: $(df -h / | awk 'NR==2{print $4}') available

## PostgreSQL Configuration
- Service Status: $(sudo systemctl is-active postgresql 2>/dev/null || echo "inactive")
- Shared Buffers: $(sudo -u postgres psql -t -c "SHOW shared_buffers;" 2>/dev/null | xargs || echo "N/A")
- Max Connections: $(sudo -u postgres psql -t -c "SHOW max_connections;" 2>/dev/null | xargs || echo "N/A")
- Active Connections: $(sudo -u postgres psql -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | xargs || echo "N/A")

## PM2 Configuration
- PM2 Status: $(command -v pm2 >/dev/null && echo "installed" || echo "not found")
- Ecosystem Config: $(test -f "$APP_DIR/ecosystem.config.js" && echo "present" || echo "missing")
- Running Instances: $(sudo -u "$UBUNTU_USER" pm2 list 2>/dev/null | grep -c "hauling-qr-server" || echo "0")

## NGINX Configuration
- Service Status: $(sudo systemctl is-active nginx 2>/dev/null || echo "inactive")
- Config Syntax: $(sudo nginx -t >/dev/null 2>&1 && echo "valid" || echo "invalid")
- Worker Processes: $(grep "worker_processes" /etc/nginx/nginx.conf 2>/dev/null | awk '{print $2}' | tr -d ';' || echo "N/A")

## System Parameters
- Swappiness: $(cat /proc/sys/vm/swappiness)
- File Max: $(cat /proc/sys/fs/file-max)
- TCP Max Connections: $(cat /proc/sys/net/core/somaxconn)

## Performance Recommendations
EOF

  # Add performance recommendations based on validation results
  if [[ $(free -g | awk '/^Mem:/{print $2}') -lt 8 ]]; then
    echo "- Consider upgrading to 8GB+ RAM for optimal performance" >> "$report_file"
  fi
  
  if [[ $(nproc) -lt 4 ]]; then
    echo "- Consider upgrading to 4+ CPU cores for optimal performance" >> "$report_file"
  fi
  
  echo "- Monitor application performance after optimization" >> "$report_file"
  echo "- Review logs regularly for performance bottlenecks" >> "$report_file"
  echo "- Consider implementing monitoring tools (htop, iotop, etc.)" >> "$report_file"
  
  log_success "✅ Performance report generated: $report_file"
  
  # Display report summary
  log_info "📋 Performance Report Summary:"
  cat "$report_file" | grep -E "^- |^## " | while read -r line; do
    if [[ $line == "## "* ]]; then
      log_info "$line"
    else
      log_info "  $line"
    fi
  done
}

# =============================================================================
# MAIN VALIDATION FUNCTION
# =============================================================================

main() {
  log_info "🔍 Starting System Optimization Validation"
  log_info "📅 Started at: $(date)"

  # Define log file for this script
  local LOG_FILE="${LOG_DIR}/validation-$(date +%Y%m%d-%H%M%S).log"
  log_info "📝 Log file: $LOG_FILE"
  
  local validation_errors=0
  
  # Validate PostgreSQL
  if ! validate_postgresql_performance; then
    validation_errors=$((validation_errors + 1))
  fi
  
  # Validate PM2
  if ! validate_pm2_cluster; then
    validation_errors=$((validation_errors + 1))
  fi
  
  # Validate NGINX
  if ! validate_nginx_performance; then
    validation_errors=$((validation_errors + 1))
  fi
  
  # Validate system parameters
  if ! validate_system_performance; then
    validation_errors=$((validation_errors + 1))
  fi
  
  # Generate performance report
  generate_performance_report
  
  # Final validation summary
  if [[ $validation_errors -eq 0 ]]; then
    log_success "✅ All system optimizations validated successfully"
    log_info "🚀 System is ready for high-performance operations"
    exit 0
  else
    log_warning "⚠️ System optimization validation completed with $validation_errors issues"
    log_info "📋 Review the performance report for recommendations"
    exit 0  # Don't fail deployment for warnings
  fi
}

# Execute main function
main "$@"
