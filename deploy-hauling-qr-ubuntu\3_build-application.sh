#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - APPLICATION BUILD MODULE
# =============================================================================
# Version: 1.0.0 - Complete application building and dependency management
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS, WSL Ubuntu
# Description: Node.js dependencies, application building, and production patches
# =============================================================================

# Source shared configuration and intelligent deployment framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/shared-config.sh"

# Load intelligent deployment framework
readonly INTELLIGENT_FRAMEWORK="${SCRIPT_DIR}/intelligent-deployment-framework.sh"
if [[ -f "$INTELLIGENT_FRAMEWORK" ]]; then
  source "$INTELLIGENT_FRAMEWORK"
else
  echo "⚠️ WARNING: Intelligent deployment framework not found: $INTELLIGENT_FRAMEWORK"
  # Define stub functions to prevent errors
  start_performance_timer() { echo "⏱️ Started timer: $1"; }
  end_performance_timer() { echo "⏱️ Completed timer: $1"; }
  generate_performance_report() { echo "📊 Performance report not available"; }
fi

# Module-specific configuration
readonly MODULE_NAME="build-application"
readonly LOG_FILE="${LOG_DIR}/build-application-$(date +%Y%m%d-%H%M%S).log"

# Setup error handling
setup_error_handling "$MODULE_NAME"

# =============================================================================
# INTELLIGENT PRODUCTION PATCHES AND CORS CONFIGURATION
# =============================================================================

# Intelligent production patches application
apply_production_patches_intelligent() {
  log_info "🧠 Analyzing production patches with intelligent detection..."

  # Check if patches were already applied recently
  local patches_marker="$APP_DIR/.production-patches-applied"

  if [[ -f "$patches_marker" ]] && installation_is_recent "$patches_marker" 24; then
    log_success "✅ Production patches were applied recently - SKIPPING"
    return 0
  fi

  # Apply patches using existing function
  if apply_production_patches; then
    # Create marker file
    echo "PRODUCTION_PATCHES_APPLIED=$(date -u +"%Y-%m-%dT%H:%M:%SZ")" > "$patches_marker"
    log_success "✅ Production patches applied and marked"
    return 0
  else
    return 1
  fi
}

# Intelligent Express.js CORS configuration
fix_express_cors_configuration_intelligent() {
  log_info "🧠 Analyzing Express.js CORS configuration with intelligent detection..."

  local server_js_file="$APP_DIR/server/server.js"

  if [[ ! -f "$server_js_file" ]]; then
    log_warning "⚠️ server.js not found at: $server_js_file - skipping CORS configuration"
    log_info "💡 This is expected in test environments without application files"
    return 0
  fi

  # Check if CORS fix is already applied
  if grep -q "NGINX_PROXY_MODE.*true" "$server_js_file"; then
    log_success "✅ Express.js CORS fix already applied - SKIPPING"
    return 0
  fi

  # Apply CORS fix using existing function
  if fix_express_cors_configuration; then
    log_success "✅ Express.js CORS configuration applied"
    return 0
  else
    return 1
  fi
}

# =============================================================================
# LEGACY EXPRESS.JS CORS CONFIGURATION FIX FOR NGINX PROXY MODE
# =============================================================================
fix_express_cors_configuration() {
  log_info "🔧 Applying Express.js CORS configuration fix for NGINX proxy mode..."

  local server_js_file="$APP_DIR/server/server.js"
  local backup_file="$APP_DIR/server/server.js.backup-$(date +%Y%m%d-%H%M%S)"

  if [[ ! -f "$server_js_file" ]]; then
    log_warning "⚠️ server.js not found at: $server_js_file - skipping CORS configuration"
    log_info "💡 This is expected in test environments without application files"
    return 0
  fi

  # Create backup
  cp "$server_js_file" "$backup_file"
  log_info "📋 Created backup: $backup_file"

  # Check if CORS fix is already applied
  if grep -q "NGINX_PROXY_MODE.*true" "$server_js_file"; then
    log_info "✅ Express.js CORS fix already applied"
    return 0
  fi

  # Apply the CORS fix by wrapping the existing CORS middleware
  log_info "🔧 Applying CORS middleware fix..."

  # Create a temporary file with the fixed CORS configuration
  cat > /tmp/cors_fix.js << 'EOF'
// CRITICAL: Check for NGINX proxy mode before applying CORS
// Check both PM2 environment variables and system environment variables
const nginxProxyMode = process.env.NGINX_PROXY_MODE === 'true' || process.env.EXPRESS_CORS_DISABLED === 'true' || process.env.CORS_HANDLED_BY_NGINX === 'true';

if (nginxProxyMode) {
  // NGINX is handling CORS - disable Express.js CORS completely
  logCorsInfo('NGINX Proxy Mode: Express.js CORS disabled', null, {
    NGINX_PROXY_MODE: process.env.NGINX_PROXY_MODE,
    EXPRESS_CORS_DISABLED: process.env.EXPRESS_CORS_DISABLED,
    CORS_HANDLED_BY_NGINX: process.env.CORS_HANDLED_BY_NGINX,
    NODE_ENV: process.env.NODE_ENV
  });

  // Add minimal middleware that does NOT set any CORS headers
  app.use((req, res, next) => {
    // Log that CORS is handled by NGINX (throttled)
    if (req.method === 'OPTIONS') {
      logCorsInfo('OPTIONS request - CORS handled by NGINX', req.headers.origin);
    }
    next();
  });
} else {
  // Normal CORS middleware when not behind NGINX proxy
  logCorsInfo('Express.js CORS Mode: Handling CORS directly', null, {
    NGINX_PROXY_MODE: process.env.NGINX_PROXY_MODE || 'false',
    EXPRESS_CORS_DISABLED: process.env.EXPRESS_CORS_DISABLED || 'false',
    CORS_HANDLED_BY_NGINX: process.env.CORS_HANDLED_BY_NGINX || 'false',
    NODE_ENV: process.env.NODE_ENV
  });
EOF

  # Find the line where CORS middleware starts
  local cors_start_line=$(grep -n "// INTELLIGENT CORS CONFIGURATION WITH THROTTLED LOGGING" "$server_js_file" | cut -d: -f1)

  if [[ -n "$cors_start_line" ]]; then
    # Find the end of the CORS middleware block
    # Look for the closing }); that ends the app.use((req, res, next) => { block
    # The pattern is: find the line with next(); followed by the closing });
    local cors_end_line

    # Find the line with "next();" after the CORS start line
    local next_line=$(sed -n "${cors_start_line},\$p" "$server_js_file" | grep -n "next();" | head -1 | cut -d: -f1)

    if [[ -n "$next_line" ]]; then
      # Calculate the actual line number in the file
      next_line=$((cors_start_line + next_line - 1))

      # Find the closing }); after the next(); line
      cors_end_line=$(sed -n "${next_line},\$p" "$server_js_file" | grep -n "^});" | head -1 | cut -d: -f1)

      if [[ -n "$cors_end_line" ]]; then
        # Calculate the actual line number in the file
        cors_end_line=$((next_line + cors_end_line - 1))
      fi
    fi

    if [[ -n "$cors_end_line" ]]; then
      # Replace the entire CORS middleware block with the conditional version
      log_info "🔧 Replacing CORS middleware block (lines $cors_start_line to $cors_end_line)..."

      # Create the complete replacement CORS middleware
      cat > /tmp/cors_replacement.js << 'EOF'
// INTELLIGENT CORS CONFIGURATION WITH THROTTLED LOGGING
const { logCorsInfo, logCorsWarn, logCorsError } = require('./utils/logger');

// Log CORS configuration once on startup (with suppression support)
logCorsInfo('CORS Configuration Initialized', null, {
  NODE_ENV: config.NODE_ENV,
  IS_DEVELOPMENT: config.IS_DEVELOPMENT,
  DEV_ENABLE_CORS_ALL: config.DEV_ENABLE_CORS_ALL,
  allowedOriginsCount: config.CORS_ORIGINS?.length || 0
});

// CRITICAL: Check for NGINX proxy mode before applying CORS
if (process.env.NGINX_PROXY_MODE === 'true' || process.env.EXPRESS_CORS_DISABLED === 'true') {
  // NGINX is handling CORS - disable Express.js CORS completely
  logCorsInfo('NGINX Proxy Mode: Express.js CORS disabled', null, {
    NGINX_PROXY_MODE: process.env.NGINX_PROXY_MODE,
    EXPRESS_CORS_DISABLED: process.env.EXPRESS_CORS_DISABLED,
    CORS_HANDLED_BY_NGINX: process.env.CORS_HANDLED_BY_NGINX
  });

  // Add minimal middleware that does NOT set any CORS headers
  app.use((req, res, next) => {
    // Log that CORS is handled by NGINX (throttled)
    if (req.method === 'OPTIONS') {
      logCorsInfo('OPTIONS request - CORS handled by NGINX', req.headers.origin);
    }
    next();
  });
} else {
  // Normal CORS middleware when not behind NGINX proxy
  logCorsInfo('Express.js CORS Mode: Handling CORS directly', null, {
    NGINX_PROXY_MODE: process.env.NGINX_PROXY_MODE || 'false',
    EXPRESS_CORS_DISABLED: process.env.EXPRESS_CORS_DISABLED || 'false'
  });

app.use((req, res, next) => {
  const origin = req.headers.origin;

  // Simplified allowed origins using centralized port configuration
  const allowedOrigins = [
    `http://localhost:${config.CLIENT_PORT}`,
    `https://localhost:${config.CLIENT_PORT}`,
    `http://localhost:${config.BACKEND_HTTP_PORT}`,
    `https://localhost:${config.HTTPS_PORT}`,
    `http://127.0.0.1:${config.CLIENT_PORT}`,
    `https://127.0.0.1:${config.CLIENT_PORT}`,
    `http://127.0.0.1:${config.BACKEND_HTTP_PORT}`,
    `https://127.0.0.1:${config.HTTPS_PORT}`,
    `http://0.0.0.0:${config.CLIENT_PORT}`,
    `https://0.0.0.0:${config.CLIENT_PORT}`,
    `http://0.0.0.0:${config.BACKEND_HTTP_PORT}`,
    `https://0.0.0.0:${config.HTTPS_PORT}`,
    // Legacy port support for backward compatibility
    'http://localhost:3000',
    'https://localhost:3000',
    'http://localhost:5000',
    'https://localhost:5000'
  ];

  // Check if origin is allowed
  if (origin && allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (config.IS_DEVELOPMENT) {
    // In development, allow various patterns
    if (origin && (
      origin.includes('localhost') ||
      origin.includes('127.0.0.1') ||
      origin.includes('0.0.0.0') ||
      origin.includes('devtunnels.ms') ||  // VS Code dev tunnels
      origin.includes('ngrok.io') ||       // ngrok tunnels
      origin.includes('localtunnel.me') || // localtunnel
      origin.includes('github.dev') ||     // GitHub Codespaces
      origin.includes('gitpod.io')         // Gitpod
    )) {
      res.header('Access-Control-Allow-Origin', origin);
      // Use intelligent logging instead of console.log
      logCorsInfo('Development origin allowed', origin, {
        method: req.method,
        path: req.path
      });
    } else {
      // Fallback to localhost:3000 for development
      res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
      if (origin) {
        logCorsWarn('Unknown development origin, using fallback', origin);
      }
    }
  } else {
    // Production: handle domain-based origins and Cloudflare proxy
    const productionDomain = process.env.PRODUCTION_DOMAIN || 'truckhaul.top';
    const allowedProductionOrigins = [
      `https://${productionDomain}`,
      `https://www.${productionDomain}`,
      `http://${productionDomain}`,
      `http://www.${productionDomain}`,
      'http://localhost:3000',  // Fallback for local testing
      'https://localhost:3000'
    ];

    if (origin && allowedProductionOrigins.includes(origin)) {
      res.header('Access-Control-Allow-Origin', origin);
      // Use intelligent logging with throttling
      logCorsInfo('Production origin allowed', origin, {
        productionDomain,
        method: req.method,
        path: req.path
      });
    } else {
      // Default to HTTPS domain for production
      const defaultOrigin = `https://${productionDomain}`;
      res.header('Access-Control-Allow-Origin', defaultOrigin);

      if (origin) {
        // Log unknown origins as warnings (throttled)
        logCorsWarn('Unknown production origin, using default', origin, {
          defaultOrigin,
          productionDomain
        });
      }
    }
  }

  // Allow ALL methods
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');


  // Allow ALL headers
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-CSRF-Token');


  // Allow credentials
  res.header('Access-Control-Allow-Credentials', 'true');


  // Expose headers
  res.header('Access-Control-Expose-Headers', 'Authorization, Content-Length, X-Requested-With');


  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  next();
});
}
EOF

      # Replace the CORS middleware block
      sed -i "${cors_start_line},${cors_end_line}d" "$server_js_file"
      sed -i "${cors_start_line}r /tmp/cors_replacement.js" "$server_js_file"

      log_success "✅ Express.js CORS configuration fix applied successfully"
      rm -f /tmp/cors_replacement.js
      return 0
    else
      log_error "❌ Could not locate end of CORS middleware block"
      cp "$backup_file" "$server_js_file"
      rm -f /tmp/cors_fix.js
      return 1
    fi
  else
    log_error "❌ Could not locate CORS middleware in server.js"
    # Restore backup
    cp "$backup_file" "$server_js_file"
    rm -f /tmp/cors_fix.js
    return 1
  fi
}

# =============================================================================
# INTELLIGENT NODE.JS DEPENDENCIES MANAGEMENT
# =============================================================================

# Intelligent Node.js dependencies verification and installation
verify_and_install_node_dependencies_intelligent() {
  log_info "🧠 Intelligent Node.js Dependencies Analysis..."

  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }

  # Verify Node.js and npm are available
  if ! command_exists node || ! command_exists npm; then
    log_error "❌ Node.js/npm not found. Please run install-system-dependencies.sh first"
    return 1
  fi

  log_info "🟢 Node.js version: $(node --version)"
  log_info "🟢 npm version: $(npm --version)"

  # Critical modules that must be available
  local -a critical_modules=("pg" "pg-pool" "pg-protocol" "pg-types" "express" "express-rate-limit" "cors" "helmet" "bcryptjs" "jsonwebtoken" "joi" "qrcode" "multer" "dotenv" "winston" "compression")

  # Check if node_modules exists and is recent
  if [[ -d "node_modules" ]] && installation_is_recent "node_modules" 24; then
    log_info "📍 node_modules directory is recent, validating integrity..."

    # Check if all critical modules are available and functional
    local missing_modules=0
    for module in "${critical_modules[@]}"; do
      if nodejs_module_exists "$module" "$APP_DIR"; then
        log_info "   ✅ $module: Available and functional"
      else
        log_info "   ❌ $module: Missing or non-functional"
        missing_modules=$((missing_modules + 1))
      fi
    done

    # If all critical modules are available, create symlinks and skip installation
    if [[ $missing_modules -eq 0 ]]; then
      log_success "✅ All critical modules are available - Creating symlinks and SKIPPING installation"

      # Ensure server symlinks are created
      create_server_module_symlinks

      create_phase_completion_marker "nodejs-dependencies" "modules-validated-$(date +%s)"
      return 0
    else
      log_warning "⚠️ $missing_modules critical modules missing - SELECTIVE INSTALLATION required"

      # Try selective installation of missing modules
      if install_missing_nodejs_modules "${critical_modules[@]}"; then
        create_server_module_symlinks
        create_phase_completion_marker "nodejs-dependencies" "selective-install-$(date +%s)"
        return 0
      else
        log_warning "⚠️ Selective installation failed - FULL REINSTALLATION required"
      fi
    fi
  else
    log_info "🔄 node_modules not found or outdated - FULL INSTALLATION required"
  fi

  # Fallback to full installation
  log_info "📦 Proceeding with full Node.js dependencies installation..."
  if verify_and_install_node_dependencies; then
    create_server_module_symlinks
    create_phase_completion_marker "nodejs-dependencies" "full-install-$(date +%s)"
    return 0
  else
    return 1
  fi
}

# Create server module symlinks for module resolution
create_server_module_symlinks() {
  log_info "🔗 Creating server module symlinks for proper resolution..."

  if [[ -d "server" ]]; then
    mkdir -p server/node_modules

    local essential_modules=("pg" "pg-pool" "pg-protocol" "pg-types" "express" "express-rate-limit" "cors" "helmet" "bcryptjs" "jsonwebtoken" "joi" "qrcode" "multer" "dotenv" "winston" "compression")
    local symlinks_created=0

    for module in "${essential_modules[@]}"; do
      if [[ -d "node_modules/$module" ]] && [[ ! -e "server/node_modules/$module" ]]; then
        if ln -sf "../../node_modules/$module" "server/node_modules/$module" 2>/dev/null; then
          symlinks_created=$((symlinks_created + 1))
        fi
      fi
    done

    log_success "✅ Created $symlinks_created server module symlinks"
  fi
}

# Install only missing Node.js modules
install_missing_nodejs_modules() {
  local -a required_modules=("$@")
  local -a missing_modules=()

  log_info "🔍 Identifying missing Node.js modules..."

  for module in "${required_modules[@]}"; do
    if ! nodejs_module_exists "$module" "$APP_DIR"; then
      missing_modules+=("$module")
    fi
  done

  if [[ ${#missing_modules[@]} -eq 0 ]]; then
    log_success "✅ All required modules are available"
    return 0
  fi

  log_info "📦 Installing ${#missing_modules[@]} missing modules: ${missing_modules[*]}"

  # Install missing modules with timeout
  if timeout 300 npm install "${missing_modules[@]}" --no-audit --no-fund 2>&1 | tee -a "$LOG_FILE"; then
    log_success "✅ Missing modules installed successfully"
    return 0
  else
    log_error "❌ Failed to install missing modules"
    return 1
  fi
}

# =============================================================================
# LEGACY NODE.JS DEPENDENCIES VERIFICATION AND INSTALLATION
# =============================================================================
verify_and_install_node_dependencies() {
  log_info "🔍 Verifying and installing Node.js dependencies..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  # Verify Node.js and npm are available
  if ! command_exists node; then
    log_error "❌ Node.js not found. Please run install-system-dependencies.sh first"
    return 1
  fi
  
  if ! command_exists npm; then
    log_error "❌ npm not found. Please run install-system-dependencies.sh first"
    return 1
  fi
  
  log_info "Node.js version: $(node --version)"
  log_info "npm version: $(npm --version)"

  # Clean install to avoid conflicts
  log_info "Cleaning npm cache and node_modules..."
  npm cache clean --force 2>/dev/null || true
  rm -rf node_modules package-lock.json 2>/dev/null || true

  # Fix npm cache permissions first
  log_info "🔧 Fixing npm cache permissions..."
  sudo chown -R ubuntu:ubuntu ~/.npm 2>/dev/null || true

  # Install all dependencies with timeout and error handling
  log_info "Installing all Node.js dependencies with timeout protection..."

  # Set npm timeout and registry for better reliability
  npm config set fetch-timeout 60000
  npm config set fetch-retry-mintimeout 10000
  npm config set fetch-retry-maxtimeout 60000
  npm config set registry https://registry.npmjs.org/

  # Clean npm cache before installation
  log_info "🧹 Cleaning npm cache before installation..."
  sudo npm cache clean --force 2>/dev/null || true

  # Try sudo npm install with timeout (5 minutes)
  if timeout 300 sudo npm install --no-audit --no-fund --force 2>&1 | tee -a "$LOG_FILE"; then
    log_success "✅ sudo npm install completed successfully"
  else
    log_warning "⚠️ sudo npm install failed or timed out, trying alternative methods..."

    # Clear npm cache and try again
    npm cache clean --force 2>/dev/null || true
    rm -rf node_modules package-lock.json 2>/dev/null || true

    # Try with --force and shorter timeout (3 minutes)
    if timeout 180 sudo npm install --force --no-audit --no-fund 2>&1 | tee -a "$LOG_FILE"; then
      log_success "✅ sudo npm install with --force completed"
    else
      log_error "❌ sudo npm install failed completely after timeout"
      return 1
    fi
  fi

  # Force install critical modules with better error handling
  local critical_modules=("pg" "express" "cors" "helmet" "bcryptjs" "jsonwebtoken" "joi" "qrcode" "multer" "dotenv" "winston" "compression")
  for module in "${critical_modules[@]}"; do
    log_info "Verifying critical module: $module"
    if ! node -e "require('$module')" 2>/dev/null; then
      log_info "Installing missing module: $module"
      sudo npm install "$module" --save --force 2>&1 | tee -a "$LOG_FILE" || {
        log_warning "⚠️ Failed to install $module with sudo, trying cache clean..."
        sudo npm cache clean --force 2>/dev/null || true
        sudo npm install "$module" --save --force 2>&1 | tee -a "$LOG_FILE" || true
      }
    fi
  done

  # CRITICAL FIX: Create symlinks for server module resolution
  log_info "🔧 Creating module symlinks for server directory..."
  if [[ -d "server" ]]; then
    # Ensure server/node_modules directory exists
    mkdir -p server/node_modules

    # Create symlinks for essential modules that the server looks for
    local essential_modules=("pg" "pg-pool" "pg-protocol" "pg-types" "express" "express-rate-limit" "cors" "helmet" "bcryptjs" "jsonwebtoken" "joi" "qrcode" "multer" "dotenv" "winston" "compression")

    for module in "${essential_modules[@]}"; do
      if [[ -d "node_modules/$module" ]] && [[ ! -e "server/node_modules/$module" ]]; then
        ln -sf "../../node_modules/$module" "server/node_modules/$module" 2>/dev/null || true
        log_info "   Linked module: $module"
      fi
    done

    log_success "✅ Server module symlinks created"
  fi

  log_success "✅ Node.js dependencies verification completed"
}

# =============================================================================
# PRODUCTION PATCHES
# =============================================================================
apply_production_patches() {
  log_info "🔧 Applying production patches..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  # Apply server-side patches
  if [[ -d "server" ]]; then
    log_info "Applying server production patches..."
    
    # Fix WSL server binding issues
    local server_js="server/server.js"
    if [[ -f "$server_js" ]]; then
      # Ensure server binds to 0.0.0.0 for WSL compatibility
      if grep -q "app.listen.*localhost" "$server_js"; then
        log_info "Fixing server binding for WSL compatibility..."
        sed -i 's/app\.listen.*localhost/app.listen(PORT, "0.0.0.0"/g' "$server_js"
        log_success "✅ Server binding fixed for WSL"
      fi
      
      # Fix JSON parsing issues in WSL
      if ! grep -q "app.use(express.json" "$server_js"; then
        log_info "Adding JSON parsing middleware..."
        sed -i '/const app = express();/a app.use(express.json({ limit: "50mb" }));' "$server_js"
        log_success "✅ JSON parsing middleware added"
      fi
    fi
  fi
  
  # Apply client-side patches
  if [[ -d "client" ]]; then
    log_info "Applying client production patches..."
    
    # Fix package.json for production build
    local client_package="client/package.json"
    if [[ -f "$client_package" ]]; then
      # Ensure proper build script
      if ! grep -q '"build".*"react-scripts build"' "$client_package"; then
        log_info "Fixing client build script..."
        sed -i 's/"build".*:.*".*"/"build": "react-scripts build"/g' "$client_package"
        log_success "✅ Client build script fixed"
      fi
    fi
  fi
  
  log_success "✅ Production patches applied"
}

# =============================================================================
# FRONTEND BUILD FIXES
# =============================================================================
fix_frontend_build_issues() {
  log_info "🔧 Fixing frontend build issues..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  if [[ ! -d "client" ]]; then
    log_warning "⚠️ Client directory not found, skipping frontend fixes"
    return 0
  fi
  
  cd "client" || {
    log_error "❌ Cannot access client directory"
    return 1
  }
  
  # Fix common React build issues
  log_info "Fixing React build configuration..."
  
  # Create or update .env file for client with production configuration
  log_info "Creating client .env file with production configuration..."

  # Check if parent .env exists and extract API URLs from it
  local api_url="http://localhost:${SERVER_HTTP_PORT}/api"
  local ws_url="ws://localhost:${SERVER_HTTP_PORT}"

  if [[ -f "../.env" ]]; then
    # Extract API URLs from parent .env file (which was copied from .env.prod)
    local parent_api_url=$(grep "^REACT_APP_API_URL=" "../.env" | cut -d'=' -f2- | tr -d '"')
    local parent_ws_url=$(grep "^REACT_APP_WS_URL=" "../.env" | cut -d'=' -f2- | tr -d '"')

    if [[ -n "$parent_api_url" ]]; then
      api_url="$parent_api_url"
      log_info "Using API URL from parent .env: $api_url"
    fi

    if [[ -n "$parent_ws_url" ]]; then
      ws_url="$parent_ws_url"
      log_info "Using WS URL from parent .env: $ws_url"
    fi
  elif [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
    # CRITICAL: Use dynamic domain configuration for production URLs
    local production_domain="${PRODUCTION_DOMAIN:-truckhaul.top}"
    api_url="https://api.${production_domain}/api"
    ws_url="wss://api.${production_domain}/ws"
    log_info "🌐 Using dynamic production domain: $production_domain"
    log_info "🌐 Production API URL: $api_url"
    log_info "🌐 Production WS URL: $ws_url"
  fi

  cat > .env << EOF
# React Client Environment - Generated for ${DEPLOYMENT_ENV}
GENERATE_SOURCEMAP=false
DISABLE_ESLINT_PLUGIN=true
SKIP_PREFLIGHT_CHECK=true

# API Configuration - FIXED for Cloudflare
REACT_APP_API_URL=${api_url}
REACT_APP_WS_URL=${ws_url}
REACT_APP_USE_HTTPS=false
EOF
  log_success "✅ Client .env file created with ${DEPLOYMENT_ENV} configuration"
  log_info "   API URL: ${api_url}"
  log_info "   WS URL: ${ws_url}"
  
  # Fix memory issues for large builds
  local package_json="package.json"
  if [[ -f "$package_json" ]]; then
    # Add memory optimization to build script
    if ! grep -q "max_old_space_size" "$package_json"; then
      log_info "Adding memory optimization to build script..."
      sed -i 's/"build": "react-scripts build"/"build": "node --max_old_space_size=4096 node_modules\/.bin\/react-scripts build"/g' "$package_json"
      log_success "✅ Memory optimization added to build script"
    fi
  fi
  
  cd "$APP_DIR"
  log_success "✅ Frontend build issues fixed"
}

# =============================================================================
# APPLICATION BUILDING
# =============================================================================
build_application() {
  log_info "🏗️ Building application components..."
  
  cd "$APP_DIR" || {
    log_error "❌ Cannot access application directory: $APP_DIR"
    return 1
  }
  
  # Step 1: Install root dependencies (for migration runner) with timeout
  log_info "Installing root dependencies (for migration runner)..."
  # Clean npm cache before root installation
  sudo npm cache clean --force 2>/dev/null || true

  if ! timeout 180 sudo npm install --no-audit --no-fund --force >>"$LOG_FILE" 2>&1; then
    log_warning "⚠️ Root sudo npm install failed or timed out, trying with cache clean..."
    sudo npm cache clean --force 2>/dev/null || true
    timeout 120 sudo npm install --force --no-audit --no-fund >>"$LOG_FILE" 2>&1 || true
  fi

  # Apply security fixes for root dependencies
  log_info "🔒 Applying root security fixes..."
  npm audit fix >>"$LOG_FILE" 2>&1 || true

  # Step 2: Install server dependencies
  if [[ -d "server" ]]; then
    log_info "Installing server dependencies..."
    pushd "server" >/dev/null
    
    # Clean install for server
    rm -rf node_modules package-lock.json 2>/dev/null || true
    
    # Clean npm cache before server installation
    sudo npm cache clean --force 2>/dev/null || true

    if ! timeout 180 sudo npm install --production --no-audit --no-fund --force >>"$LOG_FILE" 2>&1; then
      log_warning "⚠️ Server sudo npm install failed or timed out, trying with cache clean..."
      sudo npm cache clean --force 2>/dev/null || true
      timeout 120 sudo npm install --production --force --no-audit --no-fund >>"$LOG_FILE" 2>&1 || {
        log_error "❌ Server dependencies installation failed"
        popd >/dev/null
        return 1
      }
    fi

    # Apply security fixes for server dependencies
    log_info "🔒 Applying server security fixes..."
    npm audit fix >>"$LOG_FILE" 2>&1 || true
    
    popd >/dev/null
    log_success "✅ Server dependencies installed"
  fi

  # Step 3: Install client dependencies and build
  if [[ -d "client" ]]; then
    log_info "Installing client dependencies and building..."
    pushd "client" >/dev/null
    
    # Fix npm cache permissions for client
    sudo chown -R ubuntu:ubuntu ~/.npm 2>/dev/null || true

    # Clean previous build to ensure fresh build with new environment
    rm -rf build node_modules/.cache 2>/dev/null || true

    # Install client dependencies with timeout
    if [[ -f package-lock.json ]]; then
      timeout 180 npm ci --no-audit --no-fund >>"$LOG_FILE" 2>&1 || timeout 180 npm install --no-audit --no-fund >>"$LOG_FILE" 2>&1
    else
      timeout 180 sudo npm install --include=dev --no-audit --no-fund --force >>"$LOG_FILE" 2>&1
    fi

    # Skip security fixes to avoid breaking CRA build
    log_info "🔒 Skipping client security fixes to prevent build breakage"

    # Ensure react-scripts is available with timeout
    if [[ ! -x "node_modules/.bin/react-scripts" ]]; then
      log_info "Installing react-scripts safeguard..."
      timeout 120 sudo npm install react-scripts@5.0.1 --save-dev --no-audit --no-fund --force >>"$LOG_FILE" 2>&1 || true
    fi

    # Build the React application with environment variables
    log_info "Building React application with ${DEPLOYMENT_ENV} configuration..."

    # Use the same API URLs that were set in the client .env file
    local api_url=$(grep "^REACT_APP_API_URL=" .env | cut -d'=' -f2- | tr -d '"')
    local ws_url=$(grep "^REACT_APP_WS_URL=" .env | cut -d'=' -f2- | tr -d '"')

    # Fallback if not found in .env - CRITICAL: Use dynamic domain configuration
    if [[ -z "$api_url" ]]; then
      api_url="http://localhost:${SERVER_HTTP_PORT}/api"
      if [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
        # CRITICAL: Use dynamic domain from environment variable
        local production_domain="${PRODUCTION_DOMAIN:-truckhaul.top}"
        api_url="https://api.${production_domain}/api"
        log_info "🌐 Using dynamic production API URL: $api_url"
      fi
    fi

    if [[ -z "$ws_url" ]]; then
      ws_url="ws://localhost:${SERVER_HTTP_PORT}"
      if [[ "${DEPLOYMENT_ENV}" == "production" ]]; then
        # CRITICAL: Use dynamic domain from environment variable
        local production_domain="${PRODUCTION_DOMAIN:-truckhaul.top}"
        ws_url="wss://api.${production_domain}/ws"
        log_info "🌐 Using dynamic production WS URL: $ws_url"
      fi
    fi

    log_info "Building with API URL: $api_url"
    log_info "Building with WS URL: $ws_url"

    # Build with explicit environment variables
    if ! REACT_APP_API_URL="$api_url" REACT_APP_WS_URL="$ws_url" REACT_APP_USE_HTTPS=false npm run build >>"$LOG_FILE" 2>&1; then
      log_error "❌ React build failed"
      # Show last 20 lines of build output for debugging
      tail -20 "$LOG_FILE"
      popd >/dev/null
      return 1
    fi

    # Verify build output
    if [[ -d "build" ]] && [[ -f "build/index.html" ]]; then
      log_success "✅ React build completed successfully"
      log_info "Build size: $(du -sh build | cut -f1)"
    else
      log_error "❌ React build output not found"
      popd >/dev/null
      return 1
    fi
    
    popd >/dev/null
  fi

  log_success "✅ Application building completed"
}

# =============================================================================
# MAIN BUILD FUNCTION
# =============================================================================
main() {
  start_performance_timer "application-build-total"

  log_info "🚀 Starting Intelligent Application Build Process"
  log_info "📅 Started at: $(date)"
  log_info "📝 Log file: $LOG_FILE"
  log_info "🧠 Mode: Intelligent Dependency Management with Selective Installation"

  # Check root privileges
  check_root

  # Validate application directory exists
  if [[ ! -d "$APP_DIR" ]]; then
    log_error "❌ Application directory not found: $APP_DIR"
    log_error "Please run setup-repository-environment.sh first"
    exit 1
  fi

  # INTELLIGENT PRE-BUILD ANALYSIS
  log_info "🔍 Phase 0: Pre-Build Analysis"
  start_performance_timer "pre-build-analysis"

  # Check if we can skip this entire phase
  if should_skip_installation "application-build" "nodejs_modules" "$APP_DIR/node_modules" 24; then
    log_success "✅ Application build is recent and valid - SKIPPING entire phase"
    end_performance_timer "pre-build-analysis"
    end_performance_timer "application-build-total"
    return 0
  fi

  end_performance_timer "pre-build-analysis"

  # Step 1: Intelligent Node.js dependencies management
  log_info "📦 Phase 1: Intelligent Node.js Dependencies Management"
  start_performance_timer "nodejs-dependencies"

  if ! verify_and_install_node_dependencies_intelligent; then
    log_error "❌ Node.js dependencies verification failed"
    exit 1
  fi

  end_performance_timer "nodejs-dependencies"

  # Step 2: Apply production patches with intelligent detection
  log_info "🔧 Phase 2: Production Patches (Intelligent)"
  start_performance_timer "production-patches"

  if ! apply_production_patches_intelligent; then
    log_error "❌ Production patches failed"
    exit 1
  fi

  end_performance_timer "production-patches"

  # Step 3: Fix Express.js CORS configuration with intelligent detection
  log_info "🌐 Phase 3: Express.js CORS Configuration (Intelligent)"
  start_performance_timer "cors-configuration"

  if ! fix_express_cors_configuration_intelligent; then
    log_error "❌ Express.js CORS configuration fix failed"
    exit 1
  fi

  end_performance_timer "cors-configuration"

  # Step 4: Fix frontend build issues with intelligent detection
  log_info "🎨 Phase 4: Frontend Build Issues (Intelligent)"
  start_performance_timer "frontend-fixes"

  if ! fix_frontend_build_issues; then
    log_warning "⚠️ Frontend build fixes had issues, but continuing..."
  fi

  end_performance_timer "frontend-fixes"

  # Step 5: Build application with intelligent caching
  log_info "🏗️ Phase 5: Application Building (Intelligent)"
  start_performance_timer "application-building"

  if ! build_application; then
    log_error "❌ Application building failed"
    exit 1
  fi

  end_performance_timer "application-building"
  end_performance_timer "application-build-total"

  # Final comprehensive validation
  log_info "✅ Phase 6: Comprehensive Build Validation"
  start_performance_timer "build-validation"

  local validation_errors=0
  local warnings=0
  
  # Check root node_modules
  if [[ ! -d "$APP_DIR/node_modules" ]]; then
    log_error "❌ Root node_modules not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check server node_modules (allow symlinks)
  if [[ -d "$APP_DIR/server" ]] && [[ ! -d "$APP_DIR/server/node_modules" ]] && [[ ! -L "$APP_DIR/server/node_modules" ]]; then
    log_warning "⚠️ Server node_modules not found (may use symlinks)"
    warnings=$((warnings + 1))
  fi
  
  # Check client build
  if [[ -d "$APP_DIR/client" ]] && [[ ! -d "$APP_DIR/client/build" ]]; then
    log_error "❌ Client build directory not found"
    validation_errors=$((validation_errors + 1))
  fi
  
  # Check critical modules functionality
  cd "$APP_DIR"
  log_info "🔍 Testing critical modules functionality..."
  local critical_modules=("pg" "express" "cors" "helmet" "bcryptjs" "jsonwebtoken" "joi" "qrcode" "multer" "dotenv" "winston" "compression")
  local functional_modules=0

  for module in "${critical_modules[@]}"; do
    if nodejs_module_exists "$module" "$APP_DIR"; then
      log_info "   ✅ $module: Functional"
      functional_modules=$((functional_modules + 1))
    else
      log_warning "   ⚠️ $module: Not functional"
      warnings=$((warnings + 1))
    fi
  done

  log_info "📊 Module Functionality: $functional_modules/${#critical_modules[@]} modules functional"

  end_performance_timer "build-validation"

  # Generate performance report
  generate_performance_report

  if [[ $validation_errors -eq 0 ]]; then
    log_success "🎉 Intelligent Application Build Process completed successfully!"

    if [[ $warnings -gt 0 ]]; then
      log_info "⚠️ Completed with $warnings warnings (non-critical)"
    fi

    log_info "📋 Build Summary:"
    log_info "   ✅ Root dependencies: Installed and validated"
    log_info "   ✅ Server dependencies: Installed with symlinks"
    log_info "   ✅ Client application: Built successfully"
    log_info "   ✅ Production patches: Applied intelligently"
    log_info "   ✅ CORS configuration: Fixed for NGINX proxy mode"
    log_info "   ✅ Critical modules: $functional_modules/${#critical_modules[@]} functional"

    # Create intelligent completion marker
    create_phase_completion_marker "application-build-complete" "intelligent-build-$(date +%s)"

    return 0
  else
    log_error "❌ Application Build Process completed with $validation_errors errors"
    return 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
