# Troubleshooting Guide - Hauling QR Trip System Deployment

## 🔧 Comprehensive Troubleshooting for Ubuntu VPS Deployment

This guide covers common issues and solutions for the Hauling QR Trip System deployment with automatic IP detection.

---

## 🚨 Emergency Quick Fixes

### Immediate Actions for Failed Deployment
```bash
# Check deployment log
tail -f /var/log/hauling-deployment/auto-deploy-*.log

# Check all services status
systemctl status nginx postgresql
pm2 status

# Restart all services
systemctl restart nginx postgresql
pm2 restart all

# Check firewall
ufw status
```

---

## 🔄 PM2 "No Process Found" Issue - Complete Resolution

### Problem: PM2 shows "No process found" when running `pm2 status`

This is a common issue where PM2 daemon is not properly initialized or the ecosystem configuration is missing.

### Solution 1: Complete PM2 Reset and Restart
```bash
# 1. Kill all PM2 processes and daemon
pm2 kill
sudo pkill -f pm2

# 2. Kill all Node.js processes
sudo pkill -f "node.*server"
sudo pkill -f "node.*hauling"

# 3. Verify ports are free (check both 5000 and 8080)
sudo lsof -i :5000
sudo lsof -i :8080
# Should show nothing

# 4. Navigate to application directory
cd /var/www/hauling-qr-system

# 5. Verify ecosystem.config.js exists
ls -la ecosystem.config.js
cat ecosystem.config.js

# 6. Start PM2 with ecosystem configuration
pm2 start ecosystem.config.js --env production

# 7. Save PM2 configuration
pm2 save

# 8. Setup PM2 startup script
pm2 startup systemd -u ubuntu --hp /home/<USER>

# 9. Check status
pm2 status
pm2 list
```

### Solution 2: Ubuntu User Permission Fix
```bash
# 1. Fix PM2 directory permissions
sudo chown -R ubuntu:ubuntu /home/<USER>/.pm2
sudo chmod -R 755 /home/<USER>/.pm2

# 2. Run PM2 as ubuntu user
sudo -u ubuntu pm2 kill
sudo -u ubuntu pm2 start /var/www/hauling-qr-system/ecosystem.config.js --env production

# 3. Check status as ubuntu user
sudo -u ubuntu pm2 status
sudo -u ubuntu pm2 list
```

### Solution 3: Manual Process Start (Fallback)
```bash
# If PM2 continues to fail, start manually for testing
cd /var/www/hauling-qr-system/server
node server.js

# In another terminal, test the application
curl http://localhost:8080/health
```

### Verification Steps
```bash
# 1. Check PM2 process list
pm2 list

# 2. Check if hauling-qr-server is running
pm2 show hauling-qr-server

# 3. Check application logs
pm2 logs hauling-qr-server --lines 20

# 4. Test application endpoint
curl http://localhost:8080/health

# 5. Check port binding
netstat -tlnp | grep :8080
```

---

## Complete Clean Restart Process for PM2 (Updated for Port 8080)

**Note: The system now uses port 8080 by default for Cloudflare compatibility.**

```bash
# 1. Stop PM2
pm2 stop hauling-qr-server

# 2. Kill all Node.js processes
sudo pkill -f "node.*server"

# 3. Verify port 8080 is free
sudo lsof -i :8080
# Should show nothing

# 4. Start PM2 fresh
pm2 start server/server.js --name hauling-qr-server

# 5. Check status
pm2 status

# 6. Test application
curl http://localhost:5000/health
```



---

## 🚨 CRITICAL: Nginx "Service Not Active, Cannot Reload" Issue

### Problem Description
During deployment, you may encounter:
```
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful
nginx.service is not active, cannot reload.
```

### Immediate Resolution
```bash
# Quick fix - run this immediately
cd /var/www/hauling-qr-system/deploy-hauling-qr-ubuntu
chmod +x diagnose-nginx-issue.sh
sudo ./diagnose-nginx-issue.sh

# OR manual fix
sudo systemctl stop nginx
sudo fuser -k 80/tcp 443/tcp
sudo systemctl start nginx
sudo systemctl enable nginx
sudo systemctl status nginx
```

### Root Causes
1. **Service Never Started**: Nginx installed but never initially started
2. **Port Conflicts**: Another service using ports 80/443
3. **WSL Networking Issues**: Windows Subsystem for Linux limitations
4. **Permission Problems**: Nginx lacks privileges to bind to ports
5. **Configuration Timing**: Service stopped during configuration changes

### Detailed Diagnostic Process
```bash
# Step 1: Check service status
sudo systemctl status nginx --no-pager -l

# Step 2: Check port conflicts
sudo netstat -tlnp | grep -E ":80|:443"

# Step 3: Check processes
ps aux | grep nginx

# Step 4: Check logs
sudo journalctl -u nginx --no-pager -n 20
sudo tail -20 /var/log/nginx/error.log

# Step 5: Recovery
sudo systemctl stop nginx
sudo pkill nginx
sudo fuser -k 80/tcp 443/tcp
sleep 3
sudo systemctl start nginx
sudo systemctl enable nginx
```

### Prevention in Enhanced auto-deploy.sh
The enhanced script now includes:
- Pre-service checks before attempting reload
- Automatic port conflict resolution
- Retry logic with exponential backoff
- Detailed error logging and diagnostic integration

---

## 🔍 Enhanced Service Status Monitoring

### Comprehensive Service Status Check
The enhanced auto-deploy.sh script now includes robust service monitoring. Use these commands to check service status:

```bash
# Check all critical services at once
sudo systemctl is-active nginx postgresql
pm2 status

# Detailed service status with logs
sudo systemctl status nginx --no-pager -l
sudo systemctl status postgresql --no-pager -l
pm2 show hauling-qr-server

# Check port bindings
netstat -tlnp | grep -E ":80|:443|:5432|:8080"
# OR using ss (newer systems)
ss -tlnp | grep -E ":80|:443|:5432|:8080"
```

### Service Health Verification
```bash
# Test Nginx
curl -I http://localhost/
curl -I http://localhost:80/

# Test PostgreSQL connection
sudo -u postgres psql -c "SELECT version();"

# Test Node.js backend
curl http://localhost:8080/health
curl http://localhost:8080/api/health

# Test PM2 process
pm2 list | grep hauling-qr-server
pm2 logs hauling-qr-server --lines 5
```

### Automatic Service Recovery
If services fail to start, the enhanced deployment script will automatically:
1. Attempt to start the service up to 5 times
2. Use exponential backoff delays (2s, 4s, 8s, 16s, 30s)
3. Show detailed error logs if all attempts fail
4. Provide specific troubleshooting steps

### Manual Service Recovery Commands
```bash
# Force restart with retry logic (if you have the enhanced script)
cd /var/www/hauling-qr-system/deploy-hauling-qr-ubuntu
sudo ./auto-deploy.sh --environment production --preserve-artifacts

# Manual service recovery
sudo systemctl enable nginx postgresql
sudo systemctl start nginx postgresql
pm2 resurrect  # Restore saved PM2 processes
```

---

## 🔄 Complete Service Restart Procedures

### Full System Restart (Recommended for Most Issues)
Use this when you need to restart everything cleanly:

```bash
# 1. Stop all services in proper order
echo "Stopping PM2 applications..."
pm2 stop all

echo "Stopping Nginx..."
sudo systemctl stop nginx

echo "Stopping PostgreSQL..."
sudo systemctl stop postgresql

# 2. Wait a moment for clean shutdown
sleep 5

# 3. Start services in proper order
echo "Starting PostgreSQL..."
sudo systemctl start postgresql
sudo systemctl status postgresql --no-pager

echo "Starting Nginx..."
sudo systemctl start nginx
sudo systemctl status nginx --no-pager

echo "Starting PM2 applications..."
pm2 start all
pm2 status

# 4. Verify everything is working
echo "Testing services..."
curl -I http://localhost/
curl -I http://localhost:8080/health
```

### Quick Service Restart (For Minor Issues)
Use this for faster restarts when services are mostly working:

```bash
# Restart all services simultaneously
sudo systemctl restart nginx postgresql && pm2 restart all

# Check status
echo "Service Status:"
sudo systemctl is-active nginx postgresql
pm2 status
```

### Individual Service Restart Commands

#### PostgreSQL Database
```bash
# Restart PostgreSQL
sudo systemctl restart postgresql

# Check status and logs
sudo systemctl status postgresql
sudo tail -f /var/log/postgresql/postgresql-*.log

# Test database connection
sudo -u postgres psql -d hauling_qr_system -c "SELECT version();"
```

#### PM2 Application Server
```bash
# Restart PM2 application
pm2 restart hauling-qr-server

# Or restart all PM2 processes
pm2 restart all

# Check status and logs
pm2 status
pm2 logs hauling-qr-server --lines 20

# Test application health
curl http://localhost:5000/health
```

#### Nginx Web Server
```bash
# Restart Nginx
sudo systemctl restart nginx

# Check configuration first (recommended)
sudo nginx -t && sudo systemctl restart nginx

# Check status and logs
sudo systemctl status nginx
sudo tail -f /var/log/nginx/error.log

# Test web server
curl -I http://localhost/
```

### Service Restart with Health Checks

#### Complete Restart with Verification
```bash
#!/bin/bash
# Complete service restart with health checks

echo "🔄 Starting complete service restart..."

# Function to check service health
check_service() {
    local service=$1
    local check_cmd=$2
    
    echo "Checking $service..."
    if eval $check_cmd > /dev/null 2>&1; then
        echo "✅ $service is healthy"
        return 0
    else
        echo "❌ $service is not responding"
        return 1
    fi
}

# Stop services
echo "🛑 Stopping services..."
pm2 stop all
sudo systemctl stop nginx
sudo systemctl stop postgresql

sleep 3

# Start PostgreSQL
echo "🗄️ Starting PostgreSQL..."
sudo systemctl start postgresql
sleep 2
check_service "PostgreSQL" "sudo -u postgres psql -d hauling_qr_system -c 'SELECT 1;'"

# Start Application
echo "🚀 Starting Application..."
pm2 start all
sleep 5
check_service "Application" "curl -f http://localhost:5000/health"

# Start Nginx
echo "🌐 Starting Nginx..."
sudo systemctl start nginx
sleep 2
check_service "Nginx" "curl -f -I http://localhost/"

echo "🎉 Service restart complete!"
pm2 status
sudo systemctl status nginx postgresql --no-pager
```

### Emergency Recovery Restart
Use this when normal restart procedures fail:

```bash
# Force kill all processes
echo "🚨 Emergency restart - force killing processes..."

# Kill PM2 processes
pm2 kill
pkill -f "node.*server"

# Force restart system services
sudo systemctl kill nginx
sudo systemctl kill postgresql
sleep 2
sudo systemctl start postgresql
sudo systemctl start nginx

# Restart application from scratch
cd /var/www/hauling-qr-system
pm2 start ecosystem.config.js

# Verify recovery
echo "Checking recovery status..."
sleep 5
curl -I http://localhost/ && echo "✅ Web server OK"
curl -I http://localhost:8080/health && echo "✅ Application OK"
sudo -u postgres psql -d hauling_qr_system -c "SELECT 1;" && echo "✅ Database OK"
```

### Automated Restart Script
Create a reusable restart script:

```bash
# Create restart script
sudo tee /usr/local/bin/restart-hauling-system.sh > /dev/null << 'EOF'
#!/bin/bash
# Hauling QR System - Complete Restart Script

set -e

echo "🔄 Restarting Hauling QR Trip System..."

# Change to application directory
cd /var/www/hauling-qr-system

# Stop services
echo "Stopping services..."
pm2 stop all 2>/dev/null || true
sudo systemctl stop nginx
sudo systemctl stop postgresql

# Wait for clean shutdown
sleep 3

# Start services in order
echo "Starting PostgreSQL..."
sudo systemctl start postgresql

echo "Starting Nginx..."
sudo systemctl start nginx

echo "Starting Application..."
pm2 start ecosystem.config.js 2>/dev/null || pm2 start all

# Health checks
sleep 5
echo "Running health checks..."

# Check PostgreSQL
if sudo -u postgres psql -d hauling_qr_system -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ PostgreSQL: OK"
else
    echo "❌ PostgreSQL: FAILED"
fi

# Check Application
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ Application: OK"
else
    echo "❌ Application: FAILED"
fi

# Check Nginx
if curl -f -I http://localhost/ > /dev/null 2>&1; then
    echo "✅ Nginx: OK"
else
    echo "❌ Nginx: FAILED"
fi

echo "🎉 Restart complete!"
pm2 status
EOF

# Make script executable
sudo chmod +x /usr/local/bin/restart-hauling-system.sh

# Usage: sudo restart-hauling-system.sh
```

### Service Restart Troubleshooting

#### If PostgreSQL Won't Start
```bash
# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*.log

# Check disk space
df -h /var/lib/postgresql

# Check PostgreSQL configuration
sudo -u postgres /usr/lib/postgresql/*/bin/postgres --check-config

# Reset PostgreSQL if corrupted
sudo systemctl stop postgresql
sudo -u postgres /usr/lib/postgresql/*/bin/pg_resetwal /var/lib/postgresql/*/main
sudo systemctl start postgresql
```

#### If PM2 Won't Start
```bash
# Clear PM2 processes
pm2 kill
pm2 flush

# Check application manually
cd /var/www/hauling-qr-system
node server/server.js

# Restart with fresh PM2
pm2 start ecosystem.config.js
pm2 save
```

#### If Nginx Won't Start
```bash
# Test Nginx configuration
sudo nginx -t

# Check for port conflicts
sudo netstat -tlnp | grep :80
sudo fuser -k 80/tcp

# Restart with verbose logging
sudo systemctl restart nginx
sudo journalctl -u nginx -f
```

### Monitoring Service Health
```bash
# Create monitoring script
sudo tee /usr/local/bin/check-hauling-health.sh > /dev/null << 'EOF'
#!/bin/bash
# Health check script for Hauling QR System

echo "🏥 Hauling QR System Health Check"
echo "================================"

# Check system resources
echo "💾 System Resources:"
echo "Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "Disk: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5" used)"}')"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
echo ""

# Check services
echo "🔧 Service Status:"
services=("postgresql" "nginx")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "✅ $service: Running"
    else
        echo "❌ $service: Stopped"
    fi
done

# Check PM2
if pm2 list | grep -q "online"; then
    echo "✅ PM2 Application: Running"
else
    echo "❌ PM2 Application: Stopped"
fi
echo ""

# Check endpoints
echo "🌐 Endpoint Health:"
if curl -f -s http://localhost/ > /dev/null; then
    echo "✅ Web Frontend: Accessible"
else
    echo "❌ Web Frontend: Not accessible"
fi

if curl -f -s http://localhost:5000/health > /dev/null; then
    echo "✅ API Backend: Accessible"
else
    echo "❌ API Backend: Not accessible"
fi

# Check database
if sudo -u postgres psql -d hauling_qr_system -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database: Connected"
else
    echo "❌ Database: Connection failed"
fi

echo ""
echo "🕐 Last updated: $(date)"
EOF

sudo chmod +x /usr/local/bin/check-hauling-health.sh

# Usage: sudo check-hauling-health.sh
```

---

## 🔍 IP Detection Issues

### Problem: IP Detection Fails
**Symptoms:**
- Script shows "❌ Failed to detect VPS IP using all methods"
- Network connectivity errors during deployment

**Solutions:**

#### 1. Check Internet Connectivity
```bash
# Test basic connectivity
ping -c 4 *******
curl -I https://google.com

# Test specific IP detection services
curl -s https://ipinfo.io/ip
curl -s https://api.ipify.org
```

#### 2. Manual IP Override
```bash
# Find your IP manually
curl -s https://ipinfo.io/ip

# Set manual override
export MANUAL_IP="your.actual.ip.here"
sudo -E ./auto-deploy-enhanced.sh
```

#### 3. Network Configuration Issues
```bash
# Check network interfaces
ip addr show

# Check routing
ip route show

# Check DNS resolution
nslookup google.com
```

#### 4. Firewall Blocking Outbound Connections
```bash
# Check UFW status
ufw status

# Temporarily allow all outbound (for testing)
ufw allow out 80
ufw allow out 443

# Check iptables
iptables -L OUTPUT
```

---

## 📦 Repository and GitHub Issues

### Problem: Repository Clone Fails
**Symptoms:**
- "❌ Failed to clone repository"
- Authentication errors
- Permission denied errors

**Solutions:**

#### 1. Private Repository Access
```bash
# Generate GitHub Personal Access Token
# Go to GitHub → Settings → Developer settings → Personal access tokens
# Create token with 'repo' scope

# Set PAT environment variable
export GITHUB_PAT="your_github_personal_access_token"
sudo -E ./auto-deploy-enhanced.sh
```

#### 2. Repository URL Issues
```bash
# Use HTTPS URL (not SSH)
sudo -E ./auto-deploy-enhanced.sh --repo-url https://github.com/user/repo.git

# For public repositories
sudo -E ./auto-deploy-enhanced.sh --repo-url https://github.com/mightybadz18/hauling-qr-trip-management.git
```

#### 3. Network/Proxy Issues
```bash
# Check if behind corporate proxy
echo $http_proxy
echo $https_proxy

# Configure git proxy if needed
git config --global http.proxy http://proxy.company.com:8080
git config --global https.proxy https://proxy.company.com:8080
```

---

## 🗄️ Database Issues

### Problem: PostgreSQL Setup Fails
**Symptoms:**
- Database connection errors
- "role does not exist" errors
- Migration failures

**Solutions:**

#### 1. PostgreSQL Service Issues
```bash
# Check PostgreSQL status
systemctl status postgresql

# Start PostgreSQL
systemctl start postgresql

# Check PostgreSQL logs
tail -f /var/log/postgresql/postgresql-*.log

# Restart PostgreSQL
systemctl restart postgresql
```

#### 2. Database User/Role Issues
```bash
# Connect as postgres user
sudo -u postgres psql

# Create user manually
CREATE ROLE hauling_app LOGIN PASSWORD 'PostgreSQLPassword123';
CREATE DATABASE hauling_qr_system OWNER hauling_app;
GRANT ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;
\q
```

#### 3. Connection Issues
```bash
# Test database connection
sudo -u postgres psql -d hauling_qr_system -c "SELECT version();"

# Check PostgreSQL configuration
sudo nano /etc/postgresql/*/main/postgresql.conf
sudo nano /etc/postgresql/*/main/pg_hba.conf

# Restart after config changes
systemctl restart postgresql
```

#### 4. Migration Failures
```bash
# Run migrations manually
cd /var/www/hauling-qr-system
node ./database/run-migration.js

# Check migration table
sudo -u postgres psql -d hauling_qr_system -c "SELECT * FROM migrations;"

# Reset migrations (if needed)
sudo -u postgres psql -d hauling_qr_system -c "DROP TABLE IF EXISTS migrations;"
```

---

## 🏗️ Build and Dependencies Issues

### Problem: npm Install Fails
**Symptoms:**
- Package installation errors
- Node.js version conflicts
- Build failures

**Solutions:**

#### 1. Node.js Version Issues
```bash
# Check Node.js version
node -v
npm -v

# Install specific Node.js version
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Clear npm cache
npm cache clean --force
```

#### 2. Build Dependencies
```bash
# Install build essentials
sudo apt-get install -y build-essential python3-dev

# Install specific packages that commonly fail
cd /var/www/hauling-qr-system/server
npm install --build-from-source

cd /var/www/hauling-qr-system/client
npm install --legacy-peer-deps
```

#### 3. React Build Issues
```bash
# Build with specific settings
cd /var/www/hauling-qr-system/client
GENERATE_SOURCEMAP=false npm run build

# Clear build cache
rm -rf node_modules/.cache
rm -rf build
npm run build
```

#### 4. Memory Issues During Build
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

# Or use swap file
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

---

## 🚀 PM2 Process Issues

### Problem: PM2 Won't Start
**Symptoms:**
- "❌ Failed to start PM2 application"
- Process exits immediately
- PM2 shows "errored" status

**Solutions:**

#### 1. PM2 Process Debugging
```bash
# Check PM2 status
pm2 status

# View detailed logs
pm2 logs hauling-qr-server

# Show process details
pm2 show hauling-qr-server

# Restart with verbose logging
pm2 restart hauling-qr-server --log-type all
```

#### 2. Application Startup Issues
```bash
# Test application manually
cd /var/www/hauling-qr-system
node server/server.js

# Check environment variables
cat .env | grep -E "(NODE_ENV|DB_|PORT)"

# Test with specific environment
NODE_ENV=production node server/server.js
```

#### 3. PM2 Configuration Issues
```bash
# Recreate PM2 ecosystem
cd /var/www/hauling-qr-system
pm2 delete all
pm2 start ecosystem.config.js

# Or start manually
pm2 start server/server.js --name hauling-qr-server --env production
```

#### 4. Permission Issues
```bash
# Fix file permissions
sudo chown -R root:root /var/www/hauling-qr-system
sudo chmod -R 755 /var/www/hauling-qr-system

# Fix PM2 permissions
pm2 kill
pm2 start ecosystem.config.js
pm2 save
```

---

## 🌐 Nginx Configuration Issues

### Problem: Nginx Won't Start or Serve Content
**Symptoms:**
- 502 Bad Gateway errors
- Nginx test failures
- Static files not loading

**Solutions:**

#### 1. Nginx Configuration Testing
```bash
# Test Nginx configuration
nginx -t

# Check Nginx status
systemctl status nginx

# View Nginx error logs
tail -f /var/log/nginx/error.log

# Restart Nginx
systemctl restart nginx
```

#### 2. Configuration File Issues
```bash
# Check site configuration
cat /etc/nginx/sites-available/hauling-qr-system

# Verify symlink
ls -la /etc/nginx/sites-enabled/

# Recreate configuration
sudo rm /etc/nginx/sites-enabled/hauling-qr-system
sudo ln -s /etc/nginx/sites-available/hauling-qr-system /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx
```

#### 3. Backend Connection Issues
```bash
# Test backend directly
curl http://localhost:5000/health

# Check if backend is listening
netstat -tlnp | grep :5000
ss -tlnp | grep :5000

# Test proxy connection
curl -H "Host: truckhaul.top" http://localhost/api/health
```

#### 4. File Permissions
```bash
# Fix Nginx permissions
sudo chown -R www-data:www-data /var/www/hauling-qr-system/client/build
sudo chmod -R 755 /var/www/hauling-qr-system/client/build

# Check Nginx user
ps aux | grep nginx
```

---

## 🔒 CORS and Security Issues

### Problem: CORS Errors in Browser
**Symptoms:**
- "Access to fetch blocked by CORS policy"
- API calls failing from frontend
- Preflight request failures

**Solutions:**

#### 1. Verify CORS Configuration
```bash
# Check environment variables
cat /var/www/hauling-qr-system/.env | grep -E "(CORS|DEV_ENABLE|DETECTED_VPS_IP)"

# Test CORS manually
curl -i -X OPTIONS http://localhost:5000/api/auth/login \
  -H "Origin: http://your-detected-ip" \
  -H "Access-Control-Request-Method: POST"
```

#### 2. Development-Style CORS Issues
```bash
# Verify development settings are preserved
grep -E "(DEV_ENABLE_CORS_ALL|DEV_DISABLE_RATE_LIMITING)" /var/www/hauling-qr-system/.env

# Should show:
# DEV_ENABLE_CORS_ALL=true
# DEV_DISABLE_RATE_LIMITING=true
```

#### 3. Server CORS Logic
```bash
# Check server logs for CORS messages
pm2 logs hauling-qr-server | grep -i cors

# Look for development-style CORS messages
pm2 logs hauling-qr-server | grep "dev-style"
```

#### 4. Update CORS Origins
```bash
# Add missing origins to .env
echo "ALLOWED_ORIGINS=localhost,127.0.0.1,0.0.0.0,your-detected-ip,yourdomain.com" >> /var/www/hauling-qr-system/.env

# Restart application
pm2 restart hauling-qr-server
```

---

## 🔥 Firewall Issues

### Problem: Services Not Accessible
**Symptoms:**
- Connection timeouts
- Services unreachable from outside
- Port binding issues

**Solutions:**

#### 1. UFW Firewall Check - Hauling QR Trip System

**🔥 CRITICAL: The auto-deploy.sh script now automatically configures UFW with secure defaults!**

```bash
# Check UFW status (should be active after deployment)
ufw status verbose

# Expected UFW rules for Hauling QR Trip System:
# 22/tcp     ALLOW IN    SSH access
# 80/tcp     ALLOW IN    HTTP - Nginx frontend + Cloudflare
# 443/tcp    ALLOW IN    HTTPS - Direct access + Cloudflare
# 8080/tcp   DENY IN     Block external backend HTTP access
# 8443/tcp   DENY IN     Block external backend HTTPS access
# 5432/tcp   DENY IN     Block external PostgreSQL access
# 3000/tcp   DENY IN     Block React dev server port
# 5000/tcp   DENY IN     Block legacy API port
```

**🌐 Hauling QR Trip System Traffic Flow:**
```
✅ CORRECT: User → Cloudflare → VPS:443 → Nginx:80 → localhost:8080 → Backend API
✅ CORRECT: User → VPS:80 → Nginx:80 → localhost:8080 → Backend API
❌ BLOCKED: User → VPS:8080 → Direct Backend API access (security)
❌ BLOCKED: User → VPS:5432 → Direct PostgreSQL access (security)
✅ INTERNAL: Nginx → localhost:8080 → Backend API (works)
✅ INTERNAL: Backend → localhost:5432 → PostgreSQL (works)
```

**🔧 Manual UFW Configuration (if needed):**
```bash
# Reset and configure UFW manually
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# Allow essential services
ufw allow 22/tcp comment 'SSH access'
ufw allow 80/tcp comment 'HTTP - Nginx frontend + Cloudflare'
ufw allow 443/tcp comment 'HTTPS - Direct access + Cloudflare'

# Block backend and database ports (SECURITY)
ufw deny 8080/tcp comment 'Block external backend HTTP access'
ufw deny 8443/tcp comment 'Block external backend HTTPS access'
ufw deny 5432/tcp comment 'Block external PostgreSQL access'

# Enable firewall
ufw --force enable
```

#### UFW Status: Inactive - Is This Okay?

**When UFW Inactive is ACCEPTABLE:**
- ✅ **Cloud Provider Firewall**: Your VPS provider (DigitalOcean, AWS, Linode) has network-level firewall protection
- ✅ **Development Environment**: Testing or development server, not production
- ✅ **Behind Cloudflare Proxy**: Domain is proxied (orange cloud) through Cloudflare
- ✅ **Behind Load Balancer**: Server is behind AWS ALB, Google Cloud Load Balancer, or similar proxy
- ✅ **Minimal Services**: Only running essential services (Nginx, PostgreSQL, Node.js)

**When UFW Inactive is RISKY:**
- ❌ **Production Server**: Directly exposed to internet without other firewall protection
- ❌ **No Cloud Firewall**: VPS provider doesn't offer network-level firewall
- ❌ **Multiple Services**: Running SSH, FTP, databases, and other potentially vulnerable services
- ❌ **Sensitive Data**: Handling customer data, financial information, or business-critical systems

**Quick Security Assessment:**
```bash
# Check what services are listening on all interfaces
netstat -tlnp | grep "0.0.0.0"
ss -tlnp | grep "0.0.0.0"

# Check for unnecessary services
systemctl list-units --type=service --state=running

# Scan your own server for open ports (from another machine)
nmap -sS your-server-ip
```

**Enable UFW for Production (Recommended):**
```bash
# Enable UFW with essential rules
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# Allow essential services
ufw allow 22/tcp comment 'SSH'
ufw allow 80/tcp comment 'HTTP'
ufw allow 443/tcp comment 'HTTPS'

# Optional: Allow development port (remove in production)
ufw allow 5000/tcp comment 'Development API'

# Enable firewall
ufw --force enable

# Verify rules
ufw status verbose
```

**Cloud Provider Firewall Check:**

**DigitalOcean:**
```bash
# Check if Droplet has Cloud Firewall attached
curl -X GET "https://api.digitalocean.com/v2/firewalls" \
  -H "Authorization: Bearer YOUR_API_TOKEN"
```

**AWS EC2:**
```bash
# Check Security Groups (if using AWS CLI)
aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx
```

**Cloudflare Proxied Setup - UFW Considerations:**

**With Cloudflare Proxied (Orange Cloud):**
```bash
# Check if your domain is proxied through Cloudflare
dig yourdomain.com
# Should show Cloudflare IPs, not your VPS IP

# Verify Cloudflare is working
curl -I https://yourdomain.com
# Should show "cf-ray" header indicating Cloudflare
```

**UFW Inactive with Cloudflare - Security Analysis:**
- ✅ **Web Traffic Protected**: All HTTP/HTTPS goes through Cloudflare
- ✅ **Real IP Hidden**: Attackers can't easily find your VPS IP
- ✅ **DDoS Protection**: Cloudflare blocks malicious traffic
- ⚠️ **SSH Still Exposed**: Port 22 accessible if real IP discovered
- ⚠️ **Port 5000 Exposed**: API port accessible via direct IP (but hidden)
- ⚠️ **IP Discovery Risk**: If real IP leaked, all ports accessible

**Recommended Cloudflare + Minimal UFW Setup:**
```bash
# Minimal UFW rules for Cloudflare setup
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (essential for server management)
ufw allow 22/tcp comment 'SSH access'

# Allow HTTP/HTTPS (Cloudflare traffic)
ufw allow 80,443/tcp comment 'Web traffic via Cloudflare'

# Block direct API access (optional security layer)
# NOTE: This blocks external access to port 5000, but internal Nginx → API still works
ufw deny 5000/tcp comment 'Block direct API access'

# Enable UFW
ufw --force enable
```

**Traffic Flow with UFW Rules:**
```
✅ WORKS: User → api.yourdomain.com → Cloudflare → Port 443 → Nginx → localhost:5000 → API
❌ BLOCKED: User → **************:5000 → Direct API access
✅ WORKS: Server internal → localhost:5000 → API (for Nginx proxy)
```

**Why Your Website Still Works:**
- External users access `https://api.yourdomain.com/api/endpoint` (port 443)
- Cloudflare forwards to your VPS port 443
- Nginx receives the request and proxies to `localhost:5000`
- `localhost` connections are internal and bypass UFW rules
- API responds through the same path back to user

**Test Your Setup:**
```bash
# These should work (through proper channels):
curl https://api.yourdomain.com/api/health
curl https://yourdomain.com/

# These should be blocked (direct access):
curl http://**************:5000/api/health
curl http://api.yourdomain.com:5000/api/health

# This should work (internal access):
curl http://localhost:5000/api/health
```

**Alternative: Use Cloud Firewall Instead of UFW**
Many prefer cloud-level firewalls because:
- Blocks traffic before it reaches your server
- Better performance (less CPU usage)
- Centralized management across multiple servers
- DDoS protection integration

**Hybrid Approach (Most Secure):**
```bash
# Use both cloud firewall AND UFW
# Cloud firewall: Block obvious threats, allow legitimate traffic
# UFW: Additional layer, specific port restrictions

# Example UFW rules for hybrid setup
ufw default deny incoming
ufw allow from 10.0.0.0/8 to any port 5432 comment 'Internal DB access'
ufw allow 80,443/tcp comment 'Web traffic'
ufw allow 22/tcp comment 'SSH'
ufw enable
```

#### 2. iptables Issues
```bash
# Check iptables rules
iptables -L -n

# Flush iptables (if safe to do so)
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
```

#### 3. Cloud Provider Firewall
Many VPS providers have additional firewalls:

**DigitalOcean:**
- Check Droplet → Networking → Firewalls
- Ensure HTTP (80), HTTPS (443), SSH (22) are allowed

**AWS EC2:**
- Check Security Groups
- Ensure inbound rules allow ports 22, 80, 443

**Linode:**
- Check Cloud Firewall settings
- Verify port access rules

#### 4. Port Binding Issues - Hauling QR Trip System

**🔍 Check Hauling QR Trip System Ports:**
```bash
# Check all Hauling QR Trip System ports
netstat -tlnp | grep -E ":(22|80|443|8080|8443|5432)"
ss -tlnp | grep -E ":(22|80|443|8080|8443|5432)"

# Expected listening ports:
# :22    SSH (sshd)
# :80    Nginx (nginx)
# :443   Nginx (nginx) - if HTTPS enabled
# :8080  Node.js Backend (node)
# :8443  Node.js Backend (node) - if HTTPS enabled
# :5432  PostgreSQL (postgres) - localhost only
```

**🚨 Port Conflict Resolution:**
```bash
# Kill processes using required ports
sudo fuser -k 80/tcp    # Kill processes on port 80
sudo fuser -k 8080/tcp  # Kill processes on port 8080
sudo fuser -k 5432/tcp  # Kill processes on port 5432 (if needed)

# Check what was using the ports
sudo lsof -i :80
sudo lsof -i :8080
sudo lsof -i :5432
```

**🔧 Port Connectivity Testing:**
```bash
# Test internal connectivity (should always work)
curl -I http://localhost/                    # Nginx frontend
curl -I http://localhost:8080/health         # Backend API
curl -I http://127.0.0.1:8080/health         # Backend API (alternative)

# Test external connectivity (from another machine)
curl -I http://YOUR_VPS_IP/                  # Should work
curl -I http://YOUR_VPS_IP:8080/health       # Should be BLOCKED by UFW
curl -I http://YOUR_VPS_IP:5432/             # Should be BLOCKED by UFW

# Test database connectivity (internal only)
PGPASSWORD=PostgreSQLPassword psql -h localhost -U postgres -d hauling_qr_system -c "SELECT 1;"
```

**⚠️ Common Port Issues:**

1. **Backend API not accessible externally** (GOOD - this is intentional security)
   - External access to port 8080/8443 is blocked by UFW
   - Access should go through Nginx proxy on port 80/443

2. **Database not accessible externally** (GOOD - this is intentional security)
   - External access to port 5432 is blocked by UFW
   - Database should only be accessible from localhost

3. **Frontend not loading**
   - Check if Nginx is running: `systemctl status nginx`
   - Check if port 80 is listening: `netstat -tlnp | grep :80`
   - Check UFW allows port 80: `ufw status | grep 80`

---

## ☁️ Cloudflare Integration & Firewall Considerations

### Cloudflare + UFW Configuration

**🌐 How Cloudflare Works with Hauling QR Trip System:**
```
User Request → Cloudflare Edge → Your VPS → Nginx → Backend API

1. User visits https://truckhaul.top
2. Cloudflare terminates SSL and forwards to your VPS port 80/443
3. Nginx receives the request and serves frontend or proxies API calls
4. API calls are proxied to localhost:8080 (backend)
5. Backend connects to localhost:5432 (PostgreSQL)
```

**🔥 UFW Rules for Cloudflare:**
```bash
# These UFW rules work perfectly with Cloudflare:
ufw allow 80/tcp   # Cloudflare forwards HTTPS traffic here
ufw allow 443/tcp  # Direct HTTPS access (optional)
ufw deny 8080/tcp  # Block direct backend access (security)
ufw deny 5432/tcp  # Block direct database access (security)

# Cloudflare IPs are automatically allowed through ports 80/443
# No special UFW rules needed for Cloudflare IP ranges
```

**✅ Cloudflare Benefits with UFW:**
- **SSL Termination**: Cloudflare handles SSL, forwards HTTP to your server
- **DDoS Protection**: Cloudflare blocks attacks before they reach your server
- **Additional Security**: UFW blocks direct access to backend/database ports
- **Performance**: Cloudflare caching reduces server load

**🔧 Testing Cloudflare + UFW Setup:**
```bash
# Test that Cloudflare traffic works
curl -H "Host: truckhaul.top" http://YOUR_VPS_IP/

# Test that direct backend access is blocked (should fail)
curl http://YOUR_VPS_IP:8080/health  # Should timeout/refuse connection

# Test internal backend access works (should succeed)
curl http://localhost:8080/health     # Should return {"status":"OK"}

# Verify UFW is blocking external backend access
ufw status | grep 8080               # Should show DENY
```

**⚠️ Cloudflare Troubleshooting:**
1. **502 Bad Gateway**: Backend not responding on localhost:8080
2. **SSL/TLS Errors**: Check Cloudflare SSL mode (should be "Flexible" or "Full")
3. **Connection Timeouts**: Verify UFW allows ports 80/443
4. **API Subdomain Issues**: Ensure api.truckhaul.top points to same server

---

## 🌍 DNS and Domain Issues

### Problem: Domain Not Resolving
**Symptoms:**
- Domain doesn't point to VPS
- DNS propagation issues
- SSL certificate problems

**Solutions:**

#### 1. DNS Propagation Check
```bash
# Check DNS resolution
dig yourdomain.com
nslookup yourdomain.com

# Check from different locations
# Use online tools: whatsmydns.net, dnschecker.org
```

#### 2. Cloudflare Issues
```bash
# Check Cloudflare DNS records
# Ensure A record points to detected VPS IP
# Verify proxy status (orange cloud) is enabled

# Test direct IP access
curl -H "Host: yourdomain.com" http://your-detected-ip/
```

#### 3. SSL Certificate Issues
```bash
# Test SSL certificate
curl -I https://yourdomain.com

# Check certificate details
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Verify Cloudflare SSL mode is "Full" (not "Full Strict")
```

---

## 📊 Performance Issues

### Problem: Slow Application Response
**Symptoms:**
- Long page load times
- API timeouts
- High server load

**Solutions:**

#### 1. Resource Monitoring
```bash
# Check system resources
htop
free -h
df -h

# Check process usage
ps aux --sort=-%cpu | head -10
ps aux --sort=-%mem | head -10
```

#### 2. Database Performance
```bash
# Check PostgreSQL performance
sudo -u postgres psql -d hauling_qr_system -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC LIMIT 10;"

# Check database connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

#### 3. Node.js Performance
```bash
# Check PM2 monitoring
pm2 monit

# Increase PM2 memory limit
pm2 restart hauling-qr-server --max-memory-restart 2G

# Enable PM2 clustering (if needed)
pm2 start ecosystem.config.js --instances max
```

#### 4. Nginx Performance
```bash
# Check Nginx access logs for slow requests
tail -f /var/log/nginx/access.log | grep -E " [5-9][0-9]{2} "

# Enable Nginx caching
# Add to Nginx config:
# proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m;
```

---

## 🔄 Recovery Procedures

### Complete System Recovery
If deployment is completely broken:

#### 1. Clean Slate Recovery
```bash
# Stop all services
pm2 kill
systemctl stop nginx

# Remove application directory
rm -rf /var/www/hauling-qr-system

# Reset database (if needed)
sudo -u postgres dropdb hauling_qr_system
sudo -u postgres dropuser hauling_app

# Re-run deployment
sudo -E ./auto-deploy-enhanced.sh
```

#### 2. Partial Recovery
```bash
# Keep database, rebuild application
pm2 kill
rm -rf /var/www/hauling-qr-system/client/build
rm -rf /var/www/hauling-qr-system/node_modules

# Re-run deployment with existing database
sudo -E ./auto-deploy-enhanced.sh
```

#### 3. Configuration-Only Recovery
```bash
# Keep everything, just fix configuration
cd /var/www/hauling-qr-system

# Backup current .env
cp .env .env.backup

# Recreate .env with detected IP
export DETECTED_VPS_IP=$(curl -s https://ipinfo.io/ip)
# ... recreate .env file ...

# Restart services
pm2 restart hauling-qr-server
systemctl restart nginx
```

---

## 🔍 Complete Port Verification Guide

### Hauling QR Trip System Port Status Check

**🚀 Quick System Health Check:**
```bash
#!/bin/bash
echo "🔍 Hauling QR Trip System - Port & Service Status Check"
echo "=================================================="

# Check UFW status
echo "🔥 UFW Firewall Status:"
ufw status verbose | head -20

echo ""
echo "🌐 Listening Ports:"
netstat -tlnp | grep -E ":(22|80|443|8080|8443|5432)" | sort

echo ""
echo "🔧 Service Status:"
systemctl is-active nginx && echo "✅ Nginx: Running" || echo "❌ Nginx: Stopped"
systemctl is-active postgresql && echo "✅ PostgreSQL: Running" || echo "❌ PostgreSQL: Stopped"
pm2 list | grep -q "online" && echo "✅ PM2 Backend: Running" || echo "❌ PM2 Backend: Stopped"

echo ""
echo "🏥 Health Check Endpoints:"
curl -f -s http://localhost/ >/dev/null && echo "✅ Frontend: Accessible" || echo "❌ Frontend: Not accessible"
curl -f -s http://localhost:8080/health >/dev/null && echo "✅ Backend API: Accessible" || echo "❌ Backend API: Not accessible"

echo ""
echo "🔒 Security Verification:"
echo "External backend access (should be blocked):"
timeout 5 curl -f -s http://$(curl -s ifconfig.me):8080/health >/dev/null 2>&1 && echo "⚠️ Backend externally accessible (security risk)" || echo "✅ Backend blocked externally (secure)"
```

**📋 Expected Results:**
```
🔥 UFW Firewall Status:
Status: active
To                         Action      From
--                         ------      ----
22/tcp                     ALLOW IN    Anywhere
80/tcp                     ALLOW IN    Anywhere
443/tcp                    ALLOW IN    Anywhere
8080/tcp                   DENY IN     Anywhere
5432/tcp                   DENY IN     Anywhere

🌐 Listening Ports:
tcp  0.0.0.0:22    sshd
tcp  0.0.0.0:80    nginx
tcp  127.0.0.1:8080 node (Backend API)
tcp  127.0.0.1:5432 postgres

🔧 Service Status:
✅ Nginx: Running
✅ PostgreSQL: Running
✅ PM2 Backend: Running

🏥 Health Check Endpoints:
✅ Frontend: Accessible
✅ Backend API: Accessible

🔒 Security Verification:
✅ Backend blocked externally (secure)
```

**🚨 Troubleshooting Failed Checks:**

1. **UFW Inactive**: Run `sudo ufw enable` or re-run auto-deploy.sh
2. **Nginx Not Running**: `sudo systemctl start nginx`
3. **Backend Not Running**: `pm2 restart hauling-qr-server`
4. **PostgreSQL Not Running**: `sudo systemctl start postgresql`
5. **Backend Externally Accessible**: Check UFW rules, should deny port 8080

---

## 📞 Getting Help

### Log Collection for Support
```bash
# Collect all relevant logs
mkdir -p ~/debug-logs
cp /var/log/hauling-deployment/auto-deploy-*.log ~/debug-logs/
pm2 logs hauling-qr-server --lines 100 > ~/debug-logs/pm2.log
cp /var/log/nginx/error.log ~/debug-logs/nginx-error.log
cp /var/log/postgresql/postgresql-*.log ~/debug-logs/postgres.log
systemctl status nginx postgresql > ~/debug-logs/services-status.log

# Create system info
uname -a > ~/debug-logs/system-info.log
df -h >> ~/debug-logs/system-info.log
free -h >> ~/debug-logs/system-info.log
```

### Common Support Information Needed
- VPS provider and specifications
- Ubuntu version: `lsb_release -a`
- Auto-detected IP address
- Domain name being used
- Error messages from logs
- Steps that led to the issue

### Self-Diagnosis Checklist
- ✅ Internet connectivity working
- ✅ VPS IP detected correctly
- ✅ GitHub repository accessible
- ✅ All services running (PostgreSQL, PM2, Nginx)
- ✅ Firewall configured properly
- ✅ DNS pointing to correct IP
- ✅ SSL certificate valid
- ✅ Application responding to health checks

This troubleshooting guide covers the most common issues encountered during deployment. Most problems can be resolved by following these systematic approaches and checking the relevant logs for specific error messages.